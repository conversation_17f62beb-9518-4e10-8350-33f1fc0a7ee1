"""
安全路径映射模块
通过ID映射系统完全避免路径泄露和路径遍历问题
"""
import os
import hashlib
import json
import logging
from typing import Dict, Optional, List, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class SecurePathMapper:
    """安全路径映射器 - 用ID代替真实路径"""
    
    def __init__(self):
        """初始化路径映射器"""
        self._path_to_id: Dict[str, str] = {}  # 路径 -> ID
        self._id_to_path: Dict[str, str] = {}  # ID -> 路径
        self._id_to_metadata: Dict[str, Dict[str, Any]] = {}  # ID -> 元数据
        self._allowed_roots: List[str] = []
        
    def set_allowed_roots(self, roots: List[str]) -> None:
        """设置允许的根目录"""
        self._allowed_roots = [os.path.abspath(root) for root in roots if os.path.exists(root)]
        logger.info(f"已设置允许的根目录: {self._allowed_roots}")
    
    def _generate_path_id(self, file_path: str) -> str:
        """为文件路径生成唯一ID"""
        # 使用文件路径的哈希值作为ID，确保唯一性和不可预测性
        path_hash = hashlib.sha256(file_path.encode('utf-8')).hexdigest()
        return f"img_{path_hash[:16]}"  # 取前16位，足够唯一
    
    def _is_path_allowed(self, file_path: str) -> bool:
        """检查路径是否在允许的根目录内"""
        abs_path = os.path.abspath(file_path)
        
        for allowed_root in self._allowed_roots:
            try:
                # 检查路径是否在允许的根目录内
                common_path = os.path.commonpath([abs_path, allowed_root])
                if common_path == allowed_root:
                    return True
            except ValueError:
                # 在不同驱动器上（Windows）
                continue
        
        return False
    
    def register_file(self, file_path: str, category: str = "unknown") -> Optional[str]:
        """
        注册文件并返回安全ID
        
        Args:
            file_path: 文件的完整路径
            category: 文件类别（如 "source", "mask", "output" 等）
            
        Returns:
            文件ID，如果注册失败则返回None
        """
        # 规范化路径
        abs_path = os.path.abspath(file_path)
        
        # 检查文件是否存在
        if not os.path.exists(abs_path) or not os.path.isfile(abs_path):
            logger.warning(f"文件不存在或不是文件: {abs_path}")
            return None
        
        # 检查路径是否被允许
        if not self._is_path_allowed(abs_path):
            logger.warning(f"路径不在允许的根目录内: {abs_path}")
            return None
        
        # 如果已经注册过，直接返回现有ID
        if abs_path in self._path_to_id:
            return self._path_to_id[abs_path]
        
        # 生成新ID
        file_id = self._generate_path_id(abs_path)
        
        # 处理ID冲突（虽然概率极低）
        counter = 0
        original_id = file_id
        while file_id in self._id_to_path:
            counter += 1
            file_id = f"{original_id}_{counter}"
        
        # 注册映射
        self._path_to_id[abs_path] = file_id
        self._id_to_path[file_id] = abs_path
        
        # 存储元数据
        self._id_to_metadata[file_id] = {
            "filename": os.path.basename(abs_path),
            "category": category,
            "size": os.path.getsize(abs_path),
            "modified": os.path.getmtime(abs_path),
            "extension": os.path.splitext(abs_path)[1].lower()
        }
        
        logger.debug(f"已注册文件: {abs_path} -> {file_id}")
        return file_id
    
    def get_path_by_id(self, file_id: str) -> Optional[str]:
        """根据ID获取文件路径"""
        return self._id_to_path.get(file_id)
    
    def get_id_by_path(self, file_path: str) -> Optional[str]:
        """根据路径获取文件ID"""
        abs_path = os.path.abspath(file_path)
        return self._path_to_id.get(abs_path)
    
    def get_metadata_by_id(self, file_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取文件元数据"""
        return self._id_to_metadata.get(file_id, {}).copy()
    
    def register_directory(self, directory: str, category: str = "unknown", 
                          extensions: List[str] = None) -> List[Dict[str, str]]:
        """
        注册目录中的所有文件
        
        Args:
            directory: 目录路径
            category: 文件类别
            extensions: 允许的文件扩展名列表，如 ['.jpg', '.png']
            
        Returns:
            注册成功的文件信息列表
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif']
        
        registered_files = []
        
        if not os.path.exists(directory) or not os.path.isdir(directory):
            logger.warning(f"目录不存在: {directory}")
            return registered_files
        
        # 递归搜索文件
        for root, dirs, files in os.walk(directory):
            for filename in files:
                # 检查文件扩展名
                ext = os.path.splitext(filename)[1].lower()
                if ext not in extensions:
                    continue
                
                file_path = os.path.join(root, filename)
                file_id = self.register_file(file_path, category)
                
                if file_id:
                    metadata = self.get_metadata_by_id(file_id)
                    registered_files.append({
                        "id": file_id,
                        "name": metadata["filename"],
                        "category": category,
                        "size": metadata["size"]
                    })
        
        logger.info(f"从 {directory} 注册了 {len(registered_files)} 个文件")
        return registered_files
    
    def clear_category(self, category: str) -> None:
        """清除指定类别的所有注册文件"""
        ids_to_remove = []
        
        for file_id, metadata in self._id_to_metadata.items():
            if metadata.get("category") == category:
                ids_to_remove.append(file_id)
        
        for file_id in ids_to_remove:
            file_path = self._id_to_path.pop(file_id, None)
            if file_path:
                self._path_to_id.pop(file_path, None)
            self._id_to_metadata.pop(file_id, None)
        
        logger.info(f"已清除类别 '{category}' 的 {len(ids_to_remove)} 个文件")
    
    def refresh_directory(self, directory: str, category: str = "unknown", 
                         extensions: List[str] = None) -> List[Dict[str, str]]:
        """刷新目录注册（先清除再重新注册）"""
        self.clear_category(category)
        return self.register_directory(directory, category, extensions)
    
    def get_files_by_category(self, category: str) -> List[Dict[str, Any]]:
        """获取指定类别的所有文件"""
        files = []
        
        for file_id, metadata in self._id_to_metadata.items():
            if metadata.get("category") == category:
                files.append({
                    "id": file_id,
                    "name": metadata["filename"],
                    "size": metadata["size"],
                    "modified": metadata["modified"],
                    "extension": metadata["extension"]
                })
        
        # 按文件名排序
        files.sort(key=lambda x: x["name"])
        return files
    
    def export_mapping(self) -> Dict[str, Any]:
        """导出映射数据（用于调试）"""
        return {
            "path_to_id": self._path_to_id,
            "id_to_metadata": self._id_to_metadata,
            "allowed_roots": self._allowed_roots
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取映射器统计信息"""
        categories = {}
        for metadata in self._id_to_metadata.values():
            category = metadata.get("category", "unknown")
            categories[category] = categories.get(category, 0) + 1
        
        return {
            "total_files": len(self._id_to_path),
            "categories": categories,
            "allowed_roots": len(self._allowed_roots)
        }


# 全局实例
_global_path_mapper: Optional[SecurePathMapper] = None


def get_path_mapper() -> SecurePathMapper:
    """获取全局路径映射器实例"""
    global _global_path_mapper
    if _global_path_mapper is None:
        _global_path_mapper = SecurePathMapper()
    return _global_path_mapper


def init_path_mapper(allowed_roots: List[str]) -> None:
    """初始化全局路径映射器"""
    global _global_path_mapper
    _global_path_mapper = SecurePathMapper()
    _global_path_mapper.set_allowed_roots(allowed_roots)
    logger.info("全局路径映射器已初始化")


def register_file(file_path: str, category: str = "unknown") -> Optional[str]:
    """便捷函数：注册文件"""
    return get_path_mapper().register_file(file_path, category)


def get_file_path(file_id: str) -> Optional[str]:
    """便捷函数：根据ID获取文件路径"""
    return get_path_mapper().get_path_by_id(file_id)


