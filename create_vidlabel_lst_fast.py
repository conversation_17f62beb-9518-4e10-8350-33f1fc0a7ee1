# -*-coding:utf-8-*-
"""
SitUp Fusion项目：创建 vid pkl标签 (优化版本)
1. 按pkl名生成标签，划分训练和验证
2. 优化性能：批量扫描，减少重复I/O操作
"""
import random
import argparse
from tqdm import tqdm
import time
from pathlib import Path
from collections import defaultdict
import os
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict, Tuple
import multiprocessing as mp


def read_labels(label_pth):
    """
    获取标签信息，与文件夹关联
    """
    with open(label_pth, 'r') as flab:
        labels = [line.strip() for line in flab.readlines() if len(line) > 1]

    return labels


def scan_directory_batch(directory: str, pkl_suffix: str = '.pkl') -> List[str]:
    """
    批量扫描目录，一次性获取所有符合条件的PKL文件
    只返回JPG数量为3的目录中的PKL文件
    """
    valid_pkl_files = []
    
    # 按目录分组收集文件信息
    dir_files = defaultdict(lambda: {'pkl': [], 'jpg': 0})
    
    try:
        # 一次遍历收集所有文件信息
        for root, dirs, files in os.walk(directory):
            # 跳过隐藏目录
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            pkl_files = []
            jpg_count = 0
            
            for filename in files:
                filename_lower = filename.lower()
                if filename_lower.endswith(pkl_suffix):
                    pkl_files.append(os.path.join(root, filename))
                elif filename_lower.endswith('.jpg') or filename_lower.endswith('.jpeg'):
                    jpg_count += 1
            
            # 只保留JPG数量为3的目录中的PKL文件
            if jpg_count == 3 and pkl_files:
                valid_pkl_files.extend(pkl_files)
                
    except (PermissionError, OSError):
        pass
    
    return valid_pkl_files


def find_valid_files_multiprocess(root_directory: str, pkl_suffix: str = '.pkl', max_workers: int = None) -> List[str]:
    """
    多进程优化版文件搜索 - 直接返回有效的PKL文件
    
    Args:
        root_directory: 根目录路径
        pkl_suffix: PKL文件后缀
        max_workers: 最大进程数，默认为CPU核心数的一半
    
    Returns:
        符合条件的PKL文件完整路径列表（已过滤JPG数量不为3的情况）
    """
    if not os.path.exists(root_directory):
        return []
    
    max_workers = max_workers or max(1, mp.cpu_count() // 2)  # 使用一半CPU核心
    
    # 获取顶层子目录
    try:
        with os.scandir(root_directory) as entries:
            subdirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]
            
            # 处理根目录本身的文件
            root_valid_files = scan_directory_batch(root_directory, pkl_suffix)
    except (PermissionError, OSError):
        return []
    
    if not subdirs:
        return root_valid_files
    
    all_valid_files = root_valid_files[:]
    
    # 使用多进程并行处理子目录
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_dir = {
            executor.submit(scan_directory_batch, subdir, pkl_suffix): subdir 
            for subdir in subdirs
        }
        
        # 收集结果
        for future in as_completed(future_to_dir):
            try:
                result = future.result()
                all_valid_files.extend(result)
            except Exception as e:
                print(f"处理目录 {future_to_dir[future]} 时出错: {e}")
    
    return all_valid_files


class VideoDataFast:
    def __init__(self, val_rate, suffix='pkl', dataset_dir='datasets'):
        self.suffix = suffix
        self.val_rate = val_rate
        self.dataset_dir = dataset_dir

    def convert_dataset_fast(self, label_name, valid_pkl_list, txts_path_tup, scl_name='Scl'):
        """
        优化版本：直接处理已验证的PKL文件列表，无需再检查JPG
        """
        all_txt_path, train_txt_path, val_txt_path = txts_path_tup
        train_txt = open(train_txt_path, "a")
        val_txt = open(val_txt_path, "a")
        all_txt = open(all_txt_path, "a")

        # 按类别划分训练、验证
        all_num = len(valid_pkl_list)
        if all_num == 0:
            train_txt.close()
            val_txt.close()
            all_txt.close()
            return
            
        all_list = range(all_num)
        val_num = set(random.sample(all_list, int(self.val_rate * all_num)))  # 使用set加速查找
        
        for ik, vid_pth in enumerate(tqdm(valid_pkl_list, desc=f'处理 {scl_name}', leave=False)):
            vid_pth = str(vid_pth)
            all_txt.write(vid_pth + " " + str(label_name) + "\n")
            if ik in val_num:
                val_txt.write(vid_pth + " " + str(label_name) + "\n")
            else:
                train_txt.write(vid_pth + " " + str(label_name) + "\n")

        train_txt.close()
        val_txt.close()
        all_txt.close()

    def videos_files_convert_fast(self, data_files_path, label_list, test_mod=False, default_label=None):
        """
        优化版本的文件转换方法
        """
        if self.dataset_dir == "Fusion_datasets":
            dataset_txt_path = Path(data_files_path).parent / self.dataset_dir
        else:
            dataset_txt_path = Path(self.dataset_dir)
        Path(dataset_txt_path).mkdir(exist_ok=True)

        if test_mod:
            txt_names = ["test_all_label.txt", "test_train_label.txt", "test_val_label.txt"]
        else:
            txt_names = ["all_label.txt", "train_label.txt", "val_label.txt"]

        train_txt_path = dataset_txt_path / txt_names[1]
        val_txt_path = dataset_txt_path / txt_names[2]
        all_txt_path = dataset_txt_path / txt_names[0]
        txts_path_tup = (all_txt_path, train_txt_path, val_txt_path)
        
        # 清空文件
        for path in txts_path_tup:
            open(path, 'w').close()

        if default_label is not None:
            # 测试模式
            label_name = default_label
            for scl_dir in Path(data_files_path).glob('*'):
                if not scl_dir.is_dir():
                    continue
                
                print(f"扫描目录: {scl_dir.name}")
                start_time = time.perf_counter()
                valid_pkl_list = find_valid_files_multiprocess(str(scl_dir), f'.{self.suffix}')
                scan_time = time.perf_counter() - start_time
                print(f"扫描完成，找到 {len(valid_pkl_list)} 个有效PKL文件，耗时 {scan_time:.2f} 秒")
                
                self.convert_dataset_fast(label_name, valid_pkl_list, txts_path_tup, scl_dir.name)
        else:
            # 训练模式
            for i, label_name in enumerate(label_list):
                print(f"\n处理标签: {label_name}")
                video_files_path = Path(data_files_path) / label_name
                if not video_files_path.exists():
                    print(f"路径不存在: {video_files_path}")
                    continue
                
                scls_lst = [d for d in video_files_path.iterdir() if d.is_dir()]
                total_label_num = 0
                
                overall_start = time.perf_counter()
                
                for scl_dir in scls_lst:
                    print(f"  扫描子目录: {scl_dir.name}")
                    start_time = time.perf_counter()
                    
                    # 使用优化的多进程扫描
                    valid_pkl_list = find_valid_files_multiprocess(str(scl_dir), f'.{self.suffix}')
                    
                    scan_time = time.perf_counter() - start_time
                    print(f"    找到 {len(valid_pkl_list)} 个有效PKL文件，扫描耗时 {scan_time:.2f} 秒")
                    
                    total_label_num += len(valid_pkl_list)
                    self.convert_dataset_fast(label_name, valid_pkl_list, txts_path_tup, scl_dir.name)

                overall_end = time.perf_counter()
                print(f"标签 {label_name} 总数: {total_label_num}")
                print(f"总耗时: {overall_end - overall_start:.2f} 秒")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(prog='训练-验证标签生成 (优化版)', 
                                   description='优化版本，大幅提升处理速度')
    parser.add_argument('--data_pth', type=str,
                        default="/media/pyl/WD_Blue_1T/All_proj/classify_cheat/pose_rgb", 
                        help='训练集标签来源')
    parser.add_argument('--label_name', default="labels.txt", 
                        help='标签名在数据路径中，并与文件夹对应')
    parser.add_argument('--val_rate', default=0.15, 
                        help='验证集占整个训练集的比例')
    parser.add_argument('--dataset_dir', default='Fusion_datasets', 
                        help='txt保存文件夹名, 默认 Fusion_datasets')
    parser.add_argument('--TestMod', type=str, default=False, 
                        help='测试集模式')
    parser.add_argument('--default_label', default=None, 
                        help='测试模式下默认标签， 默认None')

    opt = parser.parse_args()

    opt.val_rate = float(opt.val_rate)
    if isinstance(opt.TestMod, str):
        opt.TestMod = opt.TestMod.lower() == 'true'

    if opt.TestMod:
        opt.val_rate = 1.0
        label_pth = "/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/labels.txt"
    else:
        label_pth = Path(opt.data_pth) / opt.label_name

    print("=== 优化版标签生成脚本 ===")
    print(f"数据路径: {opt.data_pth}")
    print(f"验证集比例: {opt.val_rate}")
    print(f"使用CPU核心数: {mp.cpu_count() // 2}")
    
    labels_lst = read_labels(label_pth)
    print(f"标签列表: {labels_lst}")

    video_data = VideoDataFast(val_rate=opt.val_rate, dataset_dir=opt.dataset_dir)
    
    total_start = time.perf_counter()
    video_data.videos_files_convert_fast(opt.data_pth, labels_lst, opt.TestMod, opt.default_label)
    total_end = time.perf_counter()
    
    print(f"\n=== 处理完成 ===")
    print(f"总耗时: {total_end - total_start:.2f} 秒")
