# 训练日志可视化脚本使用说明

## 功能介绍

`plot_training_metrics.py` 是一个用于解析和可视化 MMAction2 训练日志的 Python 脚本。它可以：

1. **解析训练日志**：自动提取训练准确率、验证准确率、训练损失和学习率数据
2. **解析vis_data数据**：自动解析vis_data/scalars.json中的详细训练数据
3. **生成综合指标图**：包含4个子图的训练指标总览
4. **生成学习率曲线图**：单独的学习率变化曲线
5. **🔥 生成综合对比图**：包含日志和vis_data的9宫格对比图表
6. **输出训练摘要**：关键指标的文字总结

## 使用方法

### 基本用法

```bash
# 生成完整的训练指标图和学习率曲线图
python plot_training_metrics.py /path/to/log/file.log

# 只生成学习率曲线图
python plot_training_metrics.py /path/to/log/file.log --lr-only

# 🔥 生成综合对比图（包含vis_data数据）
python plot_training_metrics.py /path/to/log/file.log --comprehensive
```

### 实际示例

```bash
# 使用实验4的日志
python plot_training_metrics.py tools/work_dirs/pose_rgb_fusion/20250806_045654/20250806_045654.log

# 只绘制学习率曲线
python plot_training_metrics.py tools/work_dirs/pose_rgb_fusion/20250806_045654/20250806_045654.log --lr-only

# 🔥 绘制综合对比图
python plot_training_metrics.py tools/work_dirs/pose_rgb_fusion/20250806_045654/20250806_045654.log --comprehensive
python plot_training_metrics.py tools/work_dirs/pose_rgb_fusion/20250805_045639/20250805_045639.log --comprehensive
```

## 输出文件

脚本会在日志文件所在目录生成以下文件：

1. **training_metrics.png**：包含4个子图的综合训练指标图
   - 左上：训练准确率 vs 验证准确率（精度轴每0.05为1单位）
   - 右上：训练损失 vs 验证损失曲线（对数坐标，支持验证损失）
   - 左下：学习率变化曲线（对数坐标）
   - 右下：准确率对比放大视图（精度轴每0.05为1单位）
   - 所有子图横坐标每5个epoch为1个单位

2. **learning_rate_curve.png**：单独的学习率变化曲线图

3. **🔥 comprehensive_metrics.png**：综合对比图（使用--comprehensive参数时生成）
   - 3x3布局，包含9个子图
   - **上排**：训练vs验证准确率、训练vs验证损失、学习率变化
   - **中排**：梯度范数、数据源对比（训练准确率）、数据源对比（学习率）
   - **下排**：准确率详细视图、损失详细视图、学习率详细视图
   - **颜色方案**：蓝色=训练数据，红色=验证数据，绿色=学习率，紫色=梯度范数
   - **数据源对比**：实线=日志数据，虚线=vis_data数据

## 输出示例

### 控制台输出
```
找到 200 条训练记录
找到 100 条验证记录
找到 0 条验证损失记录
成功解析日志文件: tools/work_dirs/pose_rgb_fusion/20250806_045654/20250806_045654.log
解析到 100 个epoch的数据
成功解析vis_data文件: tools/work_dirs/pose_rgb_fusion/20250806_045654/vis_data/scalars.json
vis_data解析到 100 个epoch的数据

==================================================
Training Summary
==================================================
Total epochs: 100
Final training accuracy: 0.9815
Final training loss: 0.005000
Final learning rate: 1.27e-06
Best validation accuracy: 0.9225 (Epoch 48)
Final validation accuracy: 0.9181
Training metrics plot saved to: tools/work_dirs/pose_rgb_fusion/20250806_045654/training_metrics.png
Learning rate curve saved to: tools/work_dirs/pose_rgb_fusion/20250806_045654/learning_rate_curve.png

# 使用--comprehensive参数时的额外输出
Comprehensive metrics plot saved to: tools/work_dirs/pose_rgb_fusion/20250806_045654/comprehensive_metrics.png
```

## 支持的日志格式

脚本支持标准的 MMAction2 训练日志格式，能够识别以下模式：

### 训练记录
```
Epoch(train) [1][100/200] lr: 0.001000, loss: 1.234567, top1_acc: 0.8500
```

### 验证记录
```
Epoch(val) [1][50/100] acc/top1: 0.8200
```

### vis_data记录（scalars.json）
```json
{"base_lr": 0.001, "lr": 0.001, "loss": 0.4821740284562111, "top1_acc": 0.8203125, "epoch": 5, "step": 132}
{"acc/top1": 0.8344827586206897, "step": 5}
```

## 技术特性

- **自动解析**：使用正则表达式自动提取关键指标
- **数据清洗**：每个epoch只保留最后一条训练记录，避免重复
- **智能填充**：对缺失的数据点进行合理填充
- **高质量输出**：300 DPI 高分辨率图片输出
- **无界面运行**：使用 Agg 后端，支持服务器环境运行

## 依赖要求

```python
matplotlib
numpy
re (内置)
os (内置)
sys (内置)
argparse (内置)
```

## 安装依赖

```bash
pip install matplotlib numpy
```

## 故障排除

### 1. 找不到训练记录
- 检查日志文件路径是否正确
- 确认日志文件格式符合 MMAction2 标准

### 2. 中文字体问题
- 脚本已配置使用英文标签，避免字体问题
- 如需中文显示，请安装相应中文字体

### 3. 图片无法保存
- 检查日志目录是否有写入权限
- 确认磁盘空间充足

## 自定义修改

### 修改图片尺寸
```python
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))  # 修改 figsize
```

### 修改输出分辨率
```python
plt.savefig(save_path, dpi=300, bbox_inches='tight')  # 修改 dpi 值
```

### 添加新的指标
在 `parse_log()` 方法中添加新的正则表达式模式，并在绘图方法中添加相应的绘制代码。

## 版本信息

- **版本**：1.0
- **兼容性**：MMAction2 训练日志
- **Python版本**：3.6+
- **测试环境**：Ubuntu 20.04, Python 3.8

## 更新日志

### v2.1 (2025-01-09)
- **🔥 优化图表逻辑**：同一图例中对比相关指标（训练vs验证），而非数据源
- **🔥 改进颜色方案**：蓝色=训练，红色=验证，绿色=学习率，紫色=梯度范数
- **🔥 智能数据选择**：优先使用vis_data（更详细），回退到日志数据
- **🔥 专门的数据源对比**：独立子图显示日志vs vis_data的差异
- **增强可读性**：更清晰的图例和标题

### v2.0 (2025-01-09)
- **🔥 新增综合对比功能**：支持vis_data/scalars.json数据解析和可视化
- **🔥 新增9宫格布局**：3x3综合对比图，包含日志和vis_data的所有指标
- **🔥 新增梯度范数显示**：从vis_data中提取梯度范数信息
- **🔥 新增数据源对比**：蓝色实线（日志）vs 红色虚线（vis_data）
- **增强命令行参数**：新增--comprehensive参数

### v1.1 (2025-01-09)
- **改进坐标轴刻度**：横坐标每5个epoch为1个单位，精度曲线每0.05为1个单位
- **增强损失曲线**：支持验证损失显示（如果日志中包含）
- **优化图表布局**：所有子图统一使用改进的坐标轴设置
- **提升可读性**：更精细的网格和刻度设置

### v1.0 (2025-01-09)
- 初始版本发布
- 支持训练准确率、验证准确率、训练损失、学习率的可视化
- 支持训练摘要输出
- 支持高分辨率图片输出
- 支持服务器环境无界面运行
