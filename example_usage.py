#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
独立推理脚本使用示例
==================

展示如何使用standalone_inference.py进行批量推理
"""

import os
import sys
import json
import subprocess
from pathlib import Path


def check_environment():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'torch', 'mmcv', 'mmengine', 'mmaction2', 
        'numpy', 'tqdm', 'PIL'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_files(config_path: str, checkpoint_path: str, data_path: str):
    """检查必要文件是否存在"""
    print("\n检查必要文件...")
    
    files_to_check = [
        ("配置文件", config_path),
        ("模型权重", checkpoint_path),
        ("数据目录", data_path)
    ]
    
    all_exist = True
    for name, path in files_to_check:
        if os.path.exists(path):
            print(f"✓ {name}: {path}")
        else:
            print(f"✗ {name}: {path} (不存在)")
            all_exist = False
    
    return all_exist


def run_inference_example():
    """运行推理示例"""
    print("\n" + "="*60)
    print("独立推理脚本使用示例")
    print("="*60)
    
    # 配置参数
    config = {
        "data_path": "/media/pyl/WD_Blue_1T/All_proj/classify_cheat",  # 测试数据路径
        "config_file": "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py",
        "checkpoint": "tools/work_dirs/pose_rgb_fusion/20250730_121806_padding/best_acc_top1_epoch_54.pth",
        "batch_size": 16,
        "device": "cuda:0",
        "output_file": "inference_results.json",
        "use_multi_gpu": False,
        "log_level": "INFO"
    }
    
    print("配置参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 检查环境和文件
    if not check_environment():
        print("\n❌ 环境检查失败，请先安装必要的依赖包")
        return False
    
    if not check_files(config["config_file"], config["checkpoint"], config["data_path"]):
        print("\n❌ 文件检查失败，请确保所有必要文件存在")
        print("\n请根据您的实际情况修改以下路径:")
        print(f"  数据路径: {config['data_path']}")
        print(f"  配置文件: {config['config_file']}")
        print(f"  模型权重: {config['checkpoint']}")
        return False
    
    # 构建命令
    cmd = [
        "python", "standalone_inference.py",
        "--data_path", config["data_path"],
        "--config", config["config_file"],
        "--checkpoint", config["checkpoint"],
        "--batch_size", str(config["batch_size"]),
        "--device", config["device"],
        "--output_file", config["output_file"],
        "--log_level", config["log_level"]
    ]
    
    if config["use_multi_gpu"]:
        cmd.append("--use_multi_gpu")
    
    print(f"\n执行命令:")
    print(" ".join(cmd))
    
    # 询问用户是否执行
    response = input("\n是否执行推理? (y/n): ").lower().strip()
    if response != 'y':
        print("取消执行")
        return False
    
    # 执行推理
    print("\n开始执行推理...")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        if result.returncode == 0:
            print("✅ 推理执行成功!")
            print("\n标准输出:")
            print(result.stdout)
            
            # 检查结果文件
            if os.path.exists(config["output_file"]):
                print(f"\n结果已保存到: {config['output_file']}")
                
                # 显示结果摘要
                try:
                    with open(config["output_file"], 'r') as f:
                        results = json.load(f)
                    
                    stats = results.get('statistics', {})
                    print(f"\n推理统计:")
                    print(f"  总样本数: {stats.get('total_samples', 0)}")
                    print(f"  成功样本数: {stats.get('successful_samples', 0)}")
                    print(f"  失败样本数: {stats.get('failed_samples', 0)}")
                    
                    if stats.get('start_time') and stats.get('end_time'):
                        duration = stats['end_time'] - stats['start_time']
                        print(f"  总耗时: {duration:.2f}秒")
                        
                        if stats.get('successful_samples', 0) > 0:
                            speed = stats['successful_samples'] / duration
                            print(f"  平均速度: {speed:.2f} 样本/秒")
                    
                    # 显示部分结果
                    results_data = results.get('results', [])
                    if results_data:
                        print(f"\n前5个样本的预测结果:")
                        for i, result in enumerate(results_data[:5]):
                            filename = Path(result['filename']).name
                            pred_label = result['pred_label']
                            max_score = max(result['pred_scores'])
                            print(f"  {i+1}. {filename}: 类别{pred_label} (置信度: {max_score:.4f})")
                
                except Exception as e:
                    print(f"读取结果文件时出错: {e}")
            
        else:
            print("❌ 推理执行失败!")
            print(f"返回码: {result.returncode}")
            print("\n标准错误:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 推理执行超时!")
        return False
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        return False
    
    return True


def show_usage_tips():
    """显示使用技巧"""
    print("\n" + "="*60)
    print("使用技巧和建议")
    print("="*60)
    
    tips = [
        "1. 数据准备:",
        "   - 确保数据目录包含pkl文件和对应的jpg图像",
        "   - pkl文件应包含'pred_skpts'关键点数据",
        "   - jpg文件命名格式: sample_name-0.jpg, sample_name-1.jpg, sample_name-2.jpg",
        "",
        "2. 性能优化:",
        "   - 根据GPU内存调整batch_size (推荐16-32)",
        "   - 使用--use_multi_gpu启用多GPU推理",
        "   - 设置合适的num_workers数量 (推荐4-8)",
        "",
        "3. 错误处理:",
        "   - 检查日志文件了解详细错误信息",
        "   - 单个样本失败不会影响整体推理",
        "   - 使用--log_level DEBUG获取详细调试信息",
        "",
        "4. 结果分析:",
        "   - 结果保存为JSON格式，包含预测分数和统计信息",
        "   - pred_label为预测的类别标签",
        "   - pred_scores为各类别的置信度分数",
        "",
        "5. 常见问题:",
        "   - CUDA内存不足: 减小batch_size",
        "   - 数据格式错误: 检查pkl文件结构",
        "   - 模型加载失败: 确认配置文件和权重文件匹配"
    ]
    
    for tip in tips:
        print(tip)


def main():
    """主函数"""
    print("独立推理脚本使用示例")
    print("=" * 30)
    
    # 显示使用技巧
    show_usage_tips()
    
    # 运行示例
    success = run_inference_example()
    
    if success:
        print("\n🎉 示例执行完成!")
        print("\n您可以根据需要修改配置参数，然后重新运行推理。")
    else:
        print("\n⚠️ 示例执行未完成，请检查配置和环境。")
    
    print("\n如需帮助，请查看standalone_inference.py的文档字符串。")


if __name__ == "__main__":
    main()
