#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
独立推理脚本 - 简化测试流程
=================================

功能说明：
1. 直接读取原始样本数据进行推理，跳过标签和pkl生成步骤
2. 支持批处理和GPU配置，实现边读取边推理的流水线处理
3. 复用现有配置文件和预处理管道，保持完全兼容性
4. 处理大量无标签样本数据

使用方法：
python standalone_inference.py \
    --data_path /path/to/test/data \
    --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py \
    --checkpoint /path/to/checkpoint.pth \
    --batch_size 32 \
    --device cuda:0 \

作者：基于MMAction2框架开发
"""

import os
import sys
import json
import pickle
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor
import time
import warnings

import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DataParallel, DistributedDataParallel
import numpy as np
import mmcv
import mmengine
from mmengine.config import Config
from mmengine.dataset import Compose, pseudo_collate
from mmengine.logging import MMLogger
from mmengine.utils import track_iter_progress

# MMAction2 imports
from mmaction.apis import init_recognizer, inference_recognizer
from mmaction.registry import MODELS
from mmaction.structures import ActionDataSample
from scipy.special import softmax
from ReadTypeFile_Speed import rglob_optimized



# 忽略一些警告
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

import torch.serialization
# 修复PyTorch 2.6权重加载问题
try:
    torch.serialization.add_safe_globals([
        'mmengine.logging.history_buffer.HistoryBuffer',
        'collections.OrderedDict',
        'torch.nn.modules.container.ModuleList',
        'torch.nn.modules.container.Sequential'
    ])
except (AttributeError, ImportError):
    # 对于较旧版本的PyTorch，忽略此设置
    pass


def parse_device_string(device_str: str) -> Tuple[str, List[int]]:
    """解析设备字符串，支持多GPU格式

    Args:
        device_str: 设备字符串，支持格式：
            - 'cuda:0' - 单GPU
            - 'cuda:0,1,2,3' - 多GPU (完整格式)
            - '4,5,6,7' - 多GPU (简化格式)
            - 'cpu' - CPU

    Returns:
        Tuple[str, List[int]]: (主设备, GPU ID列表)
    """
    if device_str.lower() == 'cpu':
        return 'cpu', []

    # 处理多GPU格式
    if ',' in device_str:
        # 提取GPU ID
        if device_str.startswith('cuda:'):
            # 格式: cuda:0,1,2,3
            gpu_ids_str = device_str.replace('cuda:', '')
        else:
            # 格式: 4,5,6,7
            gpu_ids_str = device_str

        try:
            gpu_ids = [int(x.strip()) for x in gpu_ids_str.split(',')]
            primary_device = f'cuda:{gpu_ids[0]}'
            return primary_device, gpu_ids
        except ValueError:
            raise ValueError(f"无效的GPU ID格式: {device_str}")

    # 单GPU格式
    if device_str.startswith('cuda:'):
        gpu_id = int(device_str.split(':')[1])
        return device_str, [gpu_id]

    # 纯数字格式 (如 '0')
    try:
        gpu_id = int(device_str)
        primary_device = f'cuda:{gpu_id}'
        return primary_device, [gpu_id]
    except ValueError:
        raise ValueError(f"无效的设备格式: {device_str}")


def setup_gpu_environment(gpu_ids):
    """

    """
    gpu_ids_str = ','.join(map(str, gpu_ids))
    os.environ['CUDA_VISIBLE_DEVICES'] = gpu_ids_str

    return



def change_files(target_pth:Path, file_pth:Path, suffixes=None, method=None):
    if suffixes is None:
        raise ValueError(f"@Moss: List should be a list like ['*.pkl']")
    for suffix in suffixes:
        file_pth = file_pth.with_suffix(suffix)
        change_onefile(method, file_pth, target_pth)

    for file_pth in [p for p in file_pth.parent.glob('*.jpg') if file_pth.stem in str(p)]:
        change_onefile(method, file_pth, target_pth)


def change_onefile(method, file_pth, target_pth):
    # try:  # 该方法在拷贝大文件时不是很友好
        dst = target_pth / file_pth.parent.name
        dst.mkdir(exist_ok=True)
        if method in ['Cut', 'cut']:
            file_pth.replace(dst / file_pth.name)
        elif method in ['Copy', 'copy']:
            (dst / file_pth.name).write_bytes(file_pth.read_bytes())


def convert_tensor_to_numpy(data: Any) -> Any:
    """将torch tensor转换为numpy array"""
    if isinstance(data, torch.Tensor):
        return data.detach().cpu().numpy()
    elif isinstance(data, (list, tuple)):
        return [convert_tensor_to_numpy(item) for item in data]
    elif isinstance(data, dict):
        return {key: convert_tensor_to_numpy(value) for key, value in data.items()}
    else:
        return data


def safe_extract_keypoints(skpt_data: Any) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
    """安全地提取关键点和分数数据"""
    try:
        if skpt_data is None or len(skpt_data) == 0:
            return None, None

        # 处理不同的数据格式
        if isinstance(skpt_data, (list, tuple)) and len(skpt_data) > 0:
            first_item = skpt_data[0]

            # 如果是torch tensor
            if hasattr(first_item, 'view') and hasattr(first_item, 'shape'):
                if len(first_item.shape) >= 1 and first_item.shape[0] >= 57:  # 17*3 + 6 = 57
                    keypoint_data = first_item[6:].view(17, 3)
                    skpt = keypoint_data[:, :2].detach().cpu().numpy()
                    score = keypoint_data[:, 2].detach().cpu().numpy()
                    return skpt, score

            # 如果是numpy array
            elif isinstance(first_item, np.ndarray):
                if len(first_item.shape) >= 1 and first_item.shape[0] >= 57:
                    keypoint_data = first_item[6:].reshape(17, 3)
                    skpt = keypoint_data[:, :2]
                    score = keypoint_data[:, 2]
                    return skpt, score

        return None, None

    except Exception as e:
        print(f"提取关键点数据时出错: {e}")
        return None, None


class StandaloneDataLoader:
    """独立数据加载器，直接处理原始数据目录"""
    
    def __init__(self, 
                 data_path: str,
                 test_pipeline: Compose,
                 batch_size: int = 1,
                 num_workers: int = 4,
                 gt_label: int = -1):
        """
        Args:
            data_path: 数据根目录路径
            test_pipeline: 预处理管道
            batch_size: 批处理大小
            num_workers: 并行加载线程数
        """
        self.data_path = Path(data_path)
        self.test_pipeline = test_pipeline
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.gt_label = gt_label
        self.logger = MMLogger.get_current_instance()
        
        # 扫描数据文件
        self.data_files = self._scan_data_files()
        self.logger.info(f"发现 {len(self.data_files)} 个数据样本")
    
    def _scan_data_files(self) -> List[Dict[str, Any]]:
        """扫描数据目录，匹配pkl文件和对应的jpg图像"""
        data_files = []
        
        # 递归搜索所有pkl文件
        # pkl_files = list(self.data_path.rglob("*.pkl"))
        pkl_files = rglob_optimized(str(self.data_path), '*.pkl', max_workers=self.num_workers)
        
        for pkl_path in pkl_files:
            # 根据pkl文件名匹配对应的jpg图像
            base_name = pkl_path.stem
            parent_dir = pkl_path.parent
            pkl_path = pkl_path.__str__()

            # 查找对应的jpg文件（通常是 base_name-0.jpg, base_name-1.jpg, base_name-2.jpg）
            jpg_files = [p for p in parent_dir.rglob('*.jpg')]
            if len(jpg_files) != 3:
                continue

            data_files.append({
                'pkl_path': pkl_path,
                'jpg_paths': jpg_files,
                'sample_name': base_name
            })
        return data_files
    
    def _load_pkl_data(self, pkl_path: str) -> Dict[str, Any]:
        """读取pkl文件数据"""
        try:
            with open(pkl_path, 'rb') as f:
                data = pickle.load(f)
            return data
        except Exception as e:
            self.logger.error(f"读取pkl文件失败 {pkl_path}: {e}")
            return {}
    
    def _construct_data_dict(self, file_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构造符合预处理管道要求的数据字典"""
        pkl_path = file_info['pkl_path']
        jpg_paths = file_info['jpg_paths']
        
        # 读取pkl数据
        pkl_data = self._load_pkl_data(pkl_path)
        if not pkl_data:
            return None
        
        try:
            # 基于custom_PoseRGB_extraction.py中的pose_getResult逻辑构造数据
            keypoint_lst = pkl_data.get('pred_skpts', [])
            
            # 提取关键点和分数
            skpts = []
            scores = []
            for skpt_ts in keypoint_lst:
                # skpts = [skpt_ts[0][6:].view(17, 3)[:, :2] for skpt_ts in keypoint_lst if skpt_ts is not None]
                # scores = [skpt_ts[0][6:].view(17, 3)[:, 2] for skpt_ts in keypoint_lst if skpt_ts is not None]


                skpt, score = safe_extract_keypoints(skpt_ts)
                if skpt is not None and score is not None:
                    skpts.append(skpt)
                    scores.append(score)
            
            if not skpts:
                self.logger.warning(f"样本 {pkl_path} 没有有效的关键点数据")
                return None
            
            # 构造数据字典
            data_dict = {
                'keypoint': np.stack(skpts, axis=0),  # (N, T, 17, 2)
                'keypoint_score': np.stack(scores, axis=0),  # (N, T, 17)
                'imgs_pth': jpg_paths,
                'img_shape': pkl_data.get('img_shape', (1080, 1920)),
                'original_shape': pkl_data.get('oriVid_shape', (1080, 1920)),
                'total_frames': len(skpts),
                'vid_frames': pkl_data.get('total_frames', len(skpts)),
                'filename': pkl_path,
                'frame_dir': Path(pkl_path).stem,
                'label': self.gt_label,  #
                'start_index': 0,
                'modality': 'Pose'
            }
            
            return data_dict
            
        except Exception as e:
            self.logger.error(f"构造数据字典失败 {pkl_path}: {e}")
            return None

    @staticmethod
    def read_file_pkl(pkl_name: str) -> dict:
        """读取pkl信息"""
        if not Path(pkl_name).exists():
            return {}
        with open(pkl_name, 'rb') as fi:
            data = pickle.load(fi)
        return data
    
    def __len__(self) -> int:
        """返回数据样本总数"""
        return len(self.data_files)
    
    def __iter__(self):
        """迭代器，支持批处理"""
        batch = []
        
        for file_info in self.data_files:
            # 构造数据字典
            data_dict = self._construct_data_dict(file_info)
            if data_dict is None:
                continue
            
            # 应用预处理管道
            try:
                processed_data = self.test_pipeline(data_dict)
                batch.append(processed_data)
                
                # 当批次满了或者是最后一个样本时，返回批次
                if len(batch) >= self.batch_size:
                    yield batch
                    batch = []
                    
            except Exception as e:
                self.logger.error(f"预处理失败 {file_info['pkl_path']}: {e}")
                continue
        
        # 返回最后一个不满的批次
        if batch:
            yield batch


class InferenceEngine:
    """推理引擎，处理模型加载和推理"""

    def __init__(self,
                 config_path: str,
                 checkpoint_path: str,
                 device: str = 'cuda:0',
                 use_multi_gpu: bool = False):
        """
        Args:
            config_path: 配置文件路径
            checkpoint_path: 模型权重路径
            device: 设备
            use_multi_gpu: 是否使用多GPU
        """
        self.config_path = config_path
        self.checkpoint_path = checkpoint_path
        self.device = device
        self.use_multi_gpu = use_multi_gpu
        self.logger = MMLogger.get_current_instance()

        # 初始化模型
        self.model = self._init_model()
        self.test_pipeline = self._build_test_pipeline()

        # 多GPU支持
        if self.use_multi_gpu and torch.cuda.device_count() > 1:
            self.logger.info(f"使用 {torch.cuda.device_count()} 个GPU进行推理")
            self.model = DataParallel(self.model)
        elif self.use_multi_gpu:
            self.logger.warning("请求使用多GPU但只检测到1个GPU，使用单GPU模式")
    
    def _init_model(self) -> nn.Module:
        """初始化模型"""
        self.logger.info(f"加载模型: {self.config_path}")
        self.logger.info(f"加载权重: {self.checkpoint_path}")

        try:
            # 尝试使用weights_only=False来解决PyTorch 2.6的兼容性问题
            model = init_recognizer(
                config=self.config_path,
                checkpoint=self.checkpoint_path,
                device=self.device
            )
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            # 尝试手动加载配置和权重
            from mmengine.config import Config
            from mmaction.registry import MODELS

            cfg = Config.fromfile(self.config_path)
            model = MODELS.build(cfg.model)

            # 手动加载权重，设置weights_only=False
            checkpoint = torch.load(self.checkpoint_path, map_location='cpu', weights_only=False)
            if 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'], strict=False)
            else:
                model.load_state_dict(checkpoint, strict=False)

            model = model.to(self.device)
            model.eval()

            # 添加cfg属性
            model.cfg = cfg

        self.logger.info("模型加载完成")
        return model
    
    def _build_test_pipeline(self) -> Compose:
        """构建预处理管道"""
        cfg = self.model.cfg
        test_pipeline_cfg = cfg.test_pipeline
        test_pipeline = Compose(test_pipeline_cfg)
        return test_pipeline
    
    def inference_batch(self, batch_data: List[Dict[str, Any]]) -> List[ActionDataSample]:
        """批量推理"""
        try:
            # 使用pseudo_collate组织批次数据
            collated_data = pseudo_collate(batch_data)

            # 执行推理
            with torch.no_grad():
                if self.use_multi_gpu and isinstance(self.model, DataParallel):
                    # 多GPU推理
                    results = self.model.module.test_step(collated_data)
                else:
                    # 单GPU推理
                    results = self.model.test_step(collated_data)

            return results

        except Exception as e:
            self.logger.error(f"批量推理失败: {e}")
            # 返回空结果列表
            return [ActionDataSample() for _ in batch_data]

    def inference_single(self, data_dict: Dict[str, Any]) -> ActionDataSample:
        """单样本推理"""
        try:
            return inference_recognizer(self.model, data_dict, self.test_pipeline)
        except Exception as e:
            self.logger.error(f"单样本推理失败: {e}")
            return ActionDataSample()


class ResultManager:
    """结果管理器，处理结果保存和统计"""
    
    def __init__(self):

        self.results = []
        self.stats = {
            'total_samples': 0,
            'successful_samples': 0,
            'failed_samples': 0,
            'start_time': None,
            'end_time': None
        }
        self.logger = MMLogger.get_current_instance()
    
    def add_result(self, filename: str, pred_scores: np.ndarray, success: bool = True):
        """添加推理结果"""
        if success and len(pred_scores) > 0:
            self.results.append({
                'filename': filename,
                'pred_scores': pred_scores.tolist(),
                'pred_label': int(np.argmax(pred_scores)),
                'max_confidence': float(np.max(pred_scores))
            })
            self.stats['successful_samples'] += 1
        else:
            # 记录失败的样本
            self.results.append({
                'filename': filename,
                'pred_scores': [],
                'pred_label': -1,
                'max_confidence': 0.0,
                'error': True
            })
            self.stats['failed_samples'] += 1

        self.stats['total_samples'] += 1

    
    def _print_statistics(self):
        """打印统计信息"""
        total_time = self.stats['end_time'] - self.stats['start_time']
        success_rate = self.stats['successful_samples'] / self.stats['total_samples'] * 100
        
        self.logger.info("=" * 50)
        self.logger.info("推理统计信息:")
        self.logger.info(f"总样本数: {self.stats['total_samples']}")
        self.logger.info(f"成功样本数: {self.stats['successful_samples']}")
        self.logger.info(f"失败样本数: {self.stats['failed_samples']}")
        self.logger.info(f"成功率: {success_rate:.2f}%")
        self.logger.info(f"总耗时: {total_time:.2f}秒")
        self.logger.info(f"平均速度: {self.stats['successful_samples']/total_time:.2f} 样本/秒")
        self.logger.info("=" * 50)



def setup_logging(log_level: str):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def write_pred_labels(filenames, pre_lab, top1_preds, labels, pred_gt_txt):
    """
    @Moss: 将
    样本路径, 预测类别, 标注类别, 预测概率
    写入label_txt
    """

    path_preds = str()


    path_preds += f'{filenames}, {pre_lab}, {labels}, {top1_preds.__str__()}\n'

    with open(pred_gt_txt, 'a+', encoding='utf-8') as fl:
        fl.write(path_preds)

    del path_preds

    return


def change_samples(_Select, dst_pth, pkl_pres):
    dir_name = Path(dst_pth).name
    dst = Path(dst_pth).parent / Path(f"{dir_name}_{_Select}")

    # err-data Copy
    pkl_pth, pred_lab, _score = pkl_pres
    dst_lab = dst / f'{dir_name}_pre{pred_lab}'

    base_name = Path(pkl_pth).parents[1].name
    dst_gt_pred = dst_lab / base_name
    dst_gt_pred.mkdir(exist_ok=True, parents=True)
    change_files(target_pth=dst_gt_pred, file_pth=Path(pkl_pth), suffixes=['.pkl', '.avi'], method=_Select)


def empty_file_del(path):
    """删除路径下所有(包含子文件夹)空文件夹"""
    for root, dirs, files in os.walk(path, topdown=False):
        if not os.listdir(root):
            os.rmdir(root)

    return



def main(args):
    """主函数"""

    
    # 设置日志
    setup_logging(args.log_level)
    logger = MMLogger.get_instance('standalone_inference', log_level=args.log_level)
    
    logger.info("开始独立推理...")
    logger.info(f"数据路径: {args.data_path}")
    logger.info(f"配置文件: {args.config}")
    logger.info(f"模型权重: {args.checkpoint}")
    logger.info(f"批处理大小: {args.batch_size}")
    logger.info(f"设备: {args.device}")

    primary_device, gpu_ids = parse_device_string(args.device)
    logger.info(f"解析设备配置: 主设备={primary_device}, GPU列表={gpu_ids}")

    # 设置GPU环境
    setup_gpu_environment(gpu_ids)

    # 确定是否使用多GPU
    use_multi_gpu = args.use_multi_gpu and len(gpu_ids) > 1
    if use_multi_gpu:
        logger.info(f"启用多GPU推理，使用GPU: {gpu_ids}")
    elif len(gpu_ids) > 1:
        logger.info(f"检测到多个GPU但未启用多GPU模式，仅使用主GPU: {primary_device}")
    
    try:
        # 初始化推理引擎
        engine = InferenceEngine(
            config_path=args.config,
            checkpoint_path=args.checkpoint,
            device=primary_device,
            use_multi_gpu=use_multi_gpu
        )
        
        # 初始化数据加载器
        data_loader = StandaloneDataLoader(
            data_path=args.data_path,
            test_pipeline=engine.test_pipeline,
            batch_size=args.batch_size,
            num_workers=args.num_workers,
            gt_label=args.gt_label
        )
        
        # 初始化结果管理器
        result_manager = ResultManager()
        result_manager.stats['start_time'] = time.time()
        
        # 开始推理
        logger.info("开始批量推理...")
        
        with tqdm(total=len(data_loader), desc="推理进度") as pbar:
            for batch_data in data_loader:
                try:
                    # 批量推理
                    results = engine.inference_batch(batch_data)
                    
                    # 处理结果
                    for i, result in enumerate(results):
                        filename = batch_data[i]['data_samples'].get('filename', f'sample_{i}')
                        default_gt = batch_data[i]['data_samples'].get('gt_label', torch.tensor(0)).__int__()
                        # 安全地获取预测分数
                        if hasattr(result, 'pred_score') and result.pred_score is not None:
                            pred_scores = result.pred_score.cpu().numpy()
                            pre_lab = np.argmax(pred_scores, axis=1)[0]
                            _score = np.round(softmax(pred_scores[0])[0], decimals=2)
                            write_pred_labels(filename, pre_lab, _score, default_gt, pred_gt_txt=Path(args.data_path).parent / 'pred_gt_labels.txt')
                            if not pre_lab == default_gt:
                                change_samples('Cut', Path(args.data_path), pkl_pres=(filename, pre_lab, _score))

                        else:
                            logger.warning(f"样本 {filename} 没有有效的预测结果")
                    
                    pbar.update(len(batch_data))
                    
                except Exception as e:
                    logger.error(f"批次推理失败: {e}")
                    pbar.update(len(batch_data))
        
        # 保存结果
        empty_file_del(args.data_path)
        logger.info("推理完成!")
        
    except Exception as e:
        logger.error(f"推理过程中发生错误: {e}")
        sys.exit(1)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='独立推理脚本')
    parser.add_argument('--data_path', type=str,
                        default="/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/伪标签/BaseSchools/Fujian_xiamenshuangshizhongxue",
                        help='测试数据路径')
    parser.add_argument('--config', type=str,
                        default='configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py',
                        help='配置文件路径')
    parser.add_argument('--checkpoint', type=str,
                        # default='tools/work_dirs/pose_rgb_fusion_warmup/20250825_163336/best_acc_top1_epoch_99.pth',
                        default='tools/work_dirs/pose_rgb_fusion_warmup/20250827_193240/best_acc_top1_epoch_90.pth',
                        help='模型权重文件路径')
    parser.add_argument('--batch_size', type=int, default=128, help='批处理大小')
    parser.add_argument('--gt_label', type=int, default=0, help='推理默认标签')
    parser.add_argument('--device', type=str, default='0,1,2,3', help='设备 (cuda:0, cpu等)')
    parser.add_argument('--num_workers', type=int, default=8,  help='数据加载线程数')
    parser.add_argument('--log_level', type=str, default='INFO',choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='日志级别')
    parser.add_argument('--use_multi_gpu', default=True, help='使用多GPU推理')

    return parser.parse_args()



if __name__ == '__main__':
    args = parse_args()
    main(args)
