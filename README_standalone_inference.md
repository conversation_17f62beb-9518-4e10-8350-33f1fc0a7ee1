# 独立推理脚本使用指南

## 概述

`standalone_inference.py` 是一个独立的推理脚本，用于简化MMAction2框架下的测试流程。该脚本可以直接读取原始样本数据进行推理，跳过手动生成标签和pkl文件的步骤，特别适合处理大量无标签样本数据。

## 主要特性

- ✅ **直接数据读取**: 跳过标签生成和pkl文件创建步骤
- ✅ **批处理支持**: 支持设置batch size，提高GPU利用率
- ✅ **多GPU支持**: 支持单卡和多卡推理
- ✅ **配置文件复用**: 直接使用现有配置文件和预处理管道
- ✅ **流水线处理**: 实现边读取边推理的高效处理
- ✅ **错误恢复**: 单个样本失败不影响整体流程
- ✅ **结果管理**: 自动保存推理结果和统计信息

## 环境要求

```bash
# 基础环境
Python >= 3.8
PyTorch >= 1.8.0
CUDA >= 11.0 (如果使用GPU)

# 必要依赖
pip install mmcv-full
pip install mmengine
pip install mmaction2
pip install numpy tqdm pillow
```

## 数据格式要求

### 目录结构
```
data_path/
├── class1/
│   ├── subdir1/
│   │   ├── sample001.pkl          # 关键点数据
│   │   ├── sample001-0.jpg        # 对应图像
│   │   ├── sample001-1.jpg
│   │   └── sample001-2.jpg
│   └── subdir2/
│       └── ...
└── class2/
    └── ...
```

### PKL文件格式
PKL文件应包含以下字段：
```python
{
    'pred_skpts': [                    # 关键点数据列表
        [tensor_data],                 # 每帧的关键点数据
        ...
    ],
    'sample_name': 'sample001',        # 样本名称
    'img_shape': (1080, 1920),         # 图像尺寸
    'oriVid_shape': (1080, 1920),      # 原始视频尺寸
    'total_frames': 30                 # 总帧数
}
```

## 使用方法

### 快速开始

1. **验证环境配置**
```bash
python validate_setup.py \
    --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py \
    --checkpoint /path/to/checkpoint.pth \
    --data_path /path/to/test/data
```

2. **使用快速启动脚本**
```bash
./quick_start.sh -d /path/to/test/data -c config.py -k checkpoint.pth
```

3. **直接运行推理**
```bash
python standalone_inference.py \
    --data_path /path/to/test/data \
    --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py \
    --checkpoint /path/to/checkpoint.pth \
    --batch_size 16 \
    --device cuda:0 \
    --output_file results.json
```

### 多GPU推理

```bash
python standalone_inference.py \
    --data_path /path/to/test/data \
    --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py \
    --checkpoint /path/to/checkpoint.pth \
    --batch_size 32 \
    --device cuda:0 \
    --use_multi_gpu \
    --output_file results.json
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--data_path` | str | 必需 | 测试数据根目录路径 |
| `--config` | str | 配置文件路径 | MMAction2配置文件 |
| `--checkpoint` | str | 必需 | 训练好的模型权重文件 |
| `--batch_size` | int | 32 | 批处理大小 |
| `--device` | str | cuda:0 | 推理设备 |
| `--num_workers` | int | 4 | 数据加载线程数 |
| `--output_file` | str | inference_results.json | 结果输出文件 |
| `--use_multi_gpu` | flag | False | 启用多GPU推理 |
| `--save_detailed` | flag | False | 保存详细推理结果 |
| `--log_level` | str | INFO | 日志级别 |

## 输出结果

### 结果文件格式

```json
{
  "results": [
    {
      "filename": "/path/to/sample.pkl",
      "pred_scores": [0.1, 0.8, 0.1],
      "pred_label": 1
    }
  ],
  "statistics": {
    "total_samples": 1000,
    "successful_samples": 995,
    "failed_samples": 5,
    "start_time": 1640995200.0,
    "end_time": 1640995800.0
  }
}
```

### 统计信息

- `total_samples`: 总样本数
- `successful_samples`: 成功推理的样本数
- `failed_samples`: 失败的样本数
- `start_time/end_time`: 开始/结束时间戳
- 自动计算成功率和处理速度

## 性能优化建议

### 1. 批处理大小调优
```bash
# GPU内存充足时
--batch_size 32

# GPU内存有限时
--batch_size 8

# CPU推理时
--batch_size 4
```

### 2. 多GPU配置
```bash
# 启用多GPU推理
--use_multi_gpu

# 检查GPU使用情况
nvidia-smi
```

### 3. 数据加载优化
```bash
# 增加数据加载线程
--num_workers 8

# SSD存储时可以增加更多线程
--num_workers 16
```

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```
   解决方案: 减小batch_size或使用CPU推理
   --batch_size 8 --device cpu
   ```

2. **数据格式错误**
   ```
   检查pkl文件是否包含'pred_skpts'字段
   确保jpg文件命名格式正确
   ```

3. **模型加载失败**
   ```
   确认配置文件和权重文件匹配
   检查文件路径是否正确
   ```

4. **推理速度慢**
   ```
   增加batch_size
   启用多GPU推理
   使用SSD存储
   ```

### 调试模式

```bash
# 启用详细日志
python standalone_inference.py \
    --log_level DEBUG \
    --batch_size 1 \
    ...
```

## 测试和验证

### 环境验证
```bash
# 完整验证
python validate_setup.py \
    --config config.py \
    --checkpoint checkpoint.pth \
    --data_path /path/to/data

# 快速验证（跳过数据格式检查）
python validate_setup.py --quick
```

### 功能测试
```bash
# 基础功能测试
python test_standalone_inference.py

# 使用示例
python example_usage.py
```

### 创建测试数据
```python
from test_standalone_inference import create_dummy_data
create_dummy_data("./test_data", num_samples=10)
```

## 与现有流程对比

### 传统流程
```bash
# 1. 生成标签文件
python create_vidlabel_adaptive.py --data_pth /path/to/data

# 2. 生成pkl数据
python custom_PoseRGB_extraction.py --video-list /path/to/labels

# 3. 执行推理
python tools/test.py --config config.py --checkpoint model.pth
```

### 独立推理流程
```bash
# 一步完成
python standalone_inference.py \
    --data_path /path/to/data \
    --config config.py \
    --checkpoint model.pth
```

## 技术实现

### 核心组件

1. **StandaloneDataLoader**: 直接扫描和加载原始数据
2. **InferenceEngine**: 模型初始化和推理执行
3. **ResultManager**: 结果保存和统计管理

### 数据适配策略

- 最大化复用现有预处理管道
- 构造符合MMAction2格式的数据字典
- 安全的数据类型转换（torch tensor ↔ numpy）

### 预处理管道集成

```python
# 直接使用配置文件中的test_pipeline
test_pipeline = Compose(cfg.test_pipeline)
processed_data = test_pipeline(raw_data_dict)
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个脚本！

### 开发环境设置
```bash
git clone <repository>
cd mmaction2_main0808
pip install -e .
```

### 代码风格
- 遵循PEP 8规范
- 添加类型提示
- 编写详细的文档字符串

## 许可证

本项目基于MMAction2框架开发，遵循相同的开源许可证。
