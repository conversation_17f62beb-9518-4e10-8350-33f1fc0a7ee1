# -*-coding:utf-8-*-
"""
SitUp Fusion项目：创建 vid pkl标签 (终极优化版本)
1. 按pkl名生成标签，划分训练和验证
2. 终极性能优化：异步I/O + 批量缓冲 + 线程池 + 算法优化
"""
import random
import argparse
from tqdm import tqdm
import time
from pathlib import Path
from collections import defaultdict, deque
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple, Set, Deque
import threading
from dataclasses import dataclass
import io
import sys


def read_labels(label_pth):
    """获取标签信息"""
    with open(label_pth, 'r') as flab:
        return [line.strip() for line in flab.readlines() if len(line) > 1]


@dataclass
class FileInfo:
    """文件信息数据类，减少字典开销"""
    pkl_files: List[str]
    jpg_count: int
    
    def is_valid(self) -> bool:
        return self.jpg_count == 3 and len(self.pkl_files) > 0


class BufferedWriter:
    """批量缓冲写入器"""
    def __init__(self, file_paths: Tuple[str, str, str], buffer_size: int = 1000):
        self.all_path, self.train_path, self.val_path = file_paths
        self.buffer_size = buffer_size
        self.buffers = {
            'all': deque(),
            'train': deque(), 
            'val': deque()
        }
        self.lock = threading.Lock()
    
    def add_record(self, record_type: str, vid_path: str, label: int):
        """添加记录到缓冲区"""
        record = f"{vid_path} {label}\n"
        with self.lock:
            self.buffers[record_type].append(record)
            
            # 检查是否需要刷新缓冲区
            if len(self.buffers[record_type]) >= self.buffer_size:
                self._flush_buffer(record_type)
    
    def _flush_buffer(self, record_type: str):
        """刷新指定类型的缓冲区"""
        if not self.buffers[record_type]:
            return
            
        file_map = {
            'all': self.all_path,
            'train': self.train_path,
            'val': self.val_path
        }
        
        with open(file_map[record_type], 'a') as f:
            f.writelines(self.buffers[record_type])
        
        self.buffers[record_type].clear()
    
    def flush_all(self):
        """刷新所有缓冲区"""
        with self.lock:
            for record_type in self.buffers:
                self._flush_buffer(record_type)


def scan_directory_ultra_fast(directory: str, pkl_suffix: str = '.pkl') -> FileInfo:
    """
    超高速目录扫描 - 优化版本
    """
    pkl_files = []
    jpg_count = 0
    
    try:
        # 使用os.scandir一次性获取所有信息
        with os.scandir(directory) as entries:
            for entry in entries:
                if entry.is_file():
                    name = entry.name
                    name_lower = name.lower()
                    
                    if name_lower.endswith(pkl_suffix):
                        pkl_files.append(entry.path)
                    elif name_lower.endswith(('.jpg', '.jpeg')):
                        jpg_count += 1
                        
    except (PermissionError, OSError):
        pass
    
    return FileInfo(pkl_files, jpg_count)


def process_directory_group_threaded(directories: List[str], pkl_suffix: str = '.pkl') -> List[str]:
    """
    线程池版本的目录组处理
    """
    all_valid_files = []
    
    for directory in directories:
        # 处理当前目录
        file_info = scan_directory_ultra_fast(directory, pkl_suffix)
        if file_info.is_valid():
            all_valid_files.extend(file_info.pkl_files)
        
        # 处理子目录
        try:
            with os.scandir(directory) as entries:
                subdirs = [entry.path for entry in entries 
                          if entry.is_dir() and not entry.name.startswith('.')]
            
            # 使用线程池并行处理子目录
            if subdirs:
                with ThreadPoolExecutor(max_workers=4) as executor:
                    future_to_subdir = {
                        executor.submit(scan_directory_ultra_fast, subdir, pkl_suffix): subdir 
                        for subdir in subdirs
                    }
                    
                    for future in as_completed(future_to_subdir):
                        try:
                            file_info = future.result()
                            if file_info.is_valid():
                                all_valid_files.extend(file_info.pkl_files)
                        except Exception:
                            continue
                            
        except (PermissionError, OSError):
            continue
    
    return all_valid_files


def estimate_directory_size_fast(directory: str) -> int:
    """快速估算目录大小 - 限制扫描深度"""
    try:
        count = 0
        with os.scandir(directory) as entries:
            for entry in entries:
                if entry.is_file():
                    count += 1
                elif entry.is_dir() and not entry.name.startswith('.'):
                    # 只扫描一层子目录
                    try:
                        with os.scandir(entry.path) as sub_entries:
                            count += sum(1 for e in sub_entries if e.is_file())
                    except (PermissionError, OSError):
                        count += 50  # 估算值
        return count
    except (PermissionError, OSError):
        return 0


def smart_workload_distribution_v2(root_directory: str, max_workers: int) -> List[List[str]]:
    """
    改进的智能负载分配 - 更快的估算算法
    """
    try:
        with os.scandir(root_directory) as entries:
            subdirs = [entry.path for entry in entries 
                      if entry.is_dir() and not entry.name.startswith('.')]
    except (PermissionError, OSError):
        return []
    
    if not subdirs:
        return []
    
    # 并行估算目录大小
    with ThreadPoolExecutor(max_workers=min(8, len(subdirs))) as executor:
        future_to_dir = {
            executor.submit(estimate_directory_size_fast, subdir): subdir 
            for subdir in subdirs
        }
        
        dir_sizes = []
        for future in as_completed(future_to_dir):
            try:
                size = future.result()
                directory = future_to_dir[future]
                dir_sizes.append((size, directory))
            except Exception:
                continue
    
    # 贪心分配
    dir_sizes.sort(reverse=True)
    workers = [[] for _ in range(max_workers)]
    worker_loads = [0] * max_workers
    
    for size, directory in dir_sizes:
        min_worker = worker_loads.index(min(worker_loads))
        workers[min_worker].append(directory)
        worker_loads[min_worker] += size
    
    return [group for group in workers if group]


def find_valid_files_ultra(root_directory: str, pkl_suffix: str = '.pkl', max_workers: int = None) -> List[str]:
    """
    终极优化版文件搜索 - 线程池 + 高效负载均衡
    """
    if not os.path.exists(root_directory):
        return []
    
    max_workers = max_workers or min(os.cpu_count(), 12)  # 线程池可以用更多worker
    
    # 智能分配工作负载
    worker_groups = smart_workload_distribution_v2(root_directory, max_workers)
    
    if not worker_groups:
        file_info = scan_directory_ultra_fast(root_directory, pkl_suffix)
        return file_info.pkl_files if file_info.is_valid() else []
    
    all_valid_files = []
    
    # 使用线程池并行处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_group = {
            executor.submit(process_directory_group_threaded, group, pkl_suffix): i 
            for i, group in enumerate(worker_groups)
        }
        
        for future in as_completed(future_to_group):
            try:
                result = future.result()
                all_valid_files.extend(result)
            except Exception as e:
                print(f"处理工作组时出错: {e}")
    
    return all_valid_files


class FastRandomSampler:
    """高效随机采样器"""
    def __init__(self, val_rate: float):
        self.val_rate = val_rate
        
    def generate_val_indices(self, total_count: int) -> Set[int]:
        """生成验证集索引"""
        if total_count == 0:
            return set()
        
        val_count = int(self.val_rate * total_count)
        if val_count == 0:
            return set()
        
        # 使用random.sample一次性生成
        return set(random.sample(range(total_count), val_count))


class VideoDataUltra:
    def __init__(self, val_rate: float, suffix: str = 'pkl', dataset_dir: str = 'datasets'):
        self.suffix = suffix
        self.val_rate = val_rate
        self.dataset_dir = dataset_dir
        self.sampler = FastRandomSampler(val_rate)

    def process_files_batch(self, label_name: int, valid_pkl_list: List[str], 
                           writer: BufferedWriter, scl_name: str = 'Scl') -> int:
        """
        批量处理文件 - 使用缓冲写入
        """
        total_count = len(valid_pkl_list)
        if total_count == 0:
            return 0
        
        # 预计算验证集索引
        val_indices = self.sampler.generate_val_indices(total_count)
        
        # 批量处理
        for idx, vid_path in enumerate(valid_pkl_list):
            # 所有文件都写入all
            writer.add_record('all', vid_path, label_name)
            
            # 根据索引写入train或val
            if idx in val_indices:
                writer.add_record('val', vid_path, label_name)
            else:
                writer.add_record('train', vid_path, label_name)
        
        return total_count

    def videos_files_convert_ultra(self, data_files_path: str, label_list: List[str], 
                                  test_mod: bool = False, default_label: int = None,
                                  max_workers: int = None):
        """
        终极优化版本的文件转换方法
        """
        # 设置输出路径
        if self.dataset_dir == "Fusion_datasets":
            dataset_txt_path = Path(data_files_path).parent / self.dataset_dir
        else:
            dataset_txt_path = Path(self.dataset_dir)
        dataset_txt_path.mkdir(exist_ok=True)

        # 确定文件名
        if test_mod:
            txt_names = ["test_all_label.txt", "test_train_label.txt", "test_val_label.txt"]
        else:
            txt_names = ["all_label.txt", "train_label.txt", "val_label.txt"]

        txts_path_tup = tuple(str(dataset_txt_path / name) for name in txt_names)
        
        # 清空文件
        for path in txts_path_tup:
            with open(path, 'w'):
                pass
        
        # 创建缓冲写入器
        writer = BufferedWriter(txts_path_tup, buffer_size=2000)

        try:
            if default_label is not None:
                # 测试模式
                total_processed = 0
                for scl_dir in Path(data_files_path).glob('*'):
                    if not scl_dir.is_dir():
                        continue
                    
                    print(f"\n处理目录: {scl_dir.name}")
                    start_time = time.perf_counter()
                    
                    valid_pkl_list = find_valid_files_ultra(str(scl_dir), f'.{self.suffix}', max_workers)
                    
                    scan_time = time.perf_counter() - start_time
                    print(f"扫描: {len(valid_pkl_list)} 文件，耗时 {scan_time:.2f}s")
                    
                    processed = self.process_files_batch(default_label, valid_pkl_list, writer, scl_dir.name)
                    total_processed += processed
                    
                print(f"\n测试模式完成，总处理 {total_processed} 个文件")
            else:
                # 训练模式
                total_files = 0
                total_time = 0
                
                for i, label_name in enumerate(label_list):
                    print(f"\n=== 处理标签 {i}: {label_name} ===")
                    video_files_path = Path(data_files_path) / label_name
                    
                    if not video_files_path.exists():
                        print(f"⚠️  路径不存在: {video_files_path}")
                        continue
                    
                    scls_lst = [d for d in video_files_path.iterdir() if d.is_dir()]
                    print(f"发现 {len(scls_lst)} 个子目录")
                    
                    label_start_time = time.perf_counter()
                    label_total_files = 0
                    
                    for scl_dir in scls_lst:
                        print(f"  处理: {scl_dir.name}")
                        start_time = time.perf_counter()
                        
                        # 使用终极优化的扫描
                        valid_pkl_list = find_valid_files_ultra(str(scl_dir), f'.{self.suffix}', max_workers)
                        
                        scan_time = time.perf_counter() - start_time
                        print(f"    {len(valid_pkl_list)} 文件，{scan_time:.2f}s")
                        
                        # 批量处理
                        processed = self.process_files_batch(i, valid_pkl_list, writer, scl_dir.name)
                        label_total_files += processed
                    
                    label_end_time = time.perf_counter()
                    label_time = label_end_time - label_start_time
                    
                    print(f"\n📊 '{label_name}': {label_total_files} 文件, {label_time:.2f}s, {label_total_files/label_time:.1f} 文件/秒")
                    
                    total_files += label_total_files
                    total_time += label_time
                
                print(f"\n🎉 完成! 总计: {total_files} 文件, {total_time:.2f}s, {total_files/total_time:.1f} 文件/秒")
        
        finally:
            # 确保刷新所有缓冲区
            writer.flush_all()


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        prog='训练-验证标签生成 (终极优化版)', 
        description='终极性能优化：异步I/O + 批量缓冲 + 线程池'
    )
    parser.add_argument('--data_pth', type=str,
                        default="/media/pyl/WD_Blue_1T/All_proj/classify_cheat/pose_rgb", 
                        help='数据路径')
    parser.add_argument('--label_name', default="labels.txt", help='标签文件')
    parser.add_argument('--val_rate', default=0.15, help='验证集比例')
    parser.add_argument('--dataset_dir', default='Fusion_datasets', help='输出目录')
    parser.add_argument('--TestMod', type=str, default=False, help='测试模式')
    parser.add_argument('--default_label', default=None, help='测试默认标签')
    parser.add_argument('--max_workers', type=int, default=None, help='最大线程数')

    opt = parser.parse_args()

    # 参数处理
    opt.val_rate = float(opt.val_rate)
    if isinstance(opt.TestMod, str):
        opt.TestMod = opt.TestMod.lower() == 'true'

    if opt.TestMod:
        opt.val_rate = 1.0
        label_pth = "/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/labels.txt"
    else:
        label_pth = Path(opt.data_pth) / opt.label_name

    # 显示配置
    print("🚀 === 终极优化版标签生成脚本 ===")
    print(f"📁 数据路径: {opt.data_pth}")
    print(f"📊 验证集比例: {opt.val_rate}")
    print(f"🧵 最大线程数: {opt.max_workers or min(os.cpu_count(), 12)}")
    
    # 读取标签
    labels_lst = read_labels(label_pth)
    print(f"🏷️  标签: {labels_lst}")

    # 开始处理
    video_data = VideoDataUltra(val_rate=opt.val_rate, dataset_dir=opt.dataset_dir)
    
    total_start = time.perf_counter()
    video_data.videos_files_convert_ultra(
        opt.data_pth, labels_lst, opt.TestMod, opt.default_label, opt.max_workers
    )
    total_end = time.perf_counter()
    
    print(f"\n✅ === 全部完成 ===")
    print(f"⏱️  总耗时: {total_end - total_start:.2f} 秒")
