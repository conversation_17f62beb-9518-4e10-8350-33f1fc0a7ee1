# -*-coding:utf-8-*-
"""
SitUp Fusion项目：创建 vid pkl标签 (大规模优化版本)
1. 按pkl名生成标签，划分训练和验证
2. 针对2W+大数据集优化：分层并行 + 智能负载均衡
"""
import random
import argparse
from tqdm import tqdm
import time
from pathlib import Path
from collections import defaultdict
import os
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict, Tuple, Generator
import multiprocessing as mp
import heapq


def read_labels(label_pth):
    """
    获取标签信息，与文件夹关联
    """
    with open(label_pth, 'r') as flab:
        labels = [line.strip() for line in flab.readlines() if len(line) > 1]
    return labels


def estimate_directory_size(directory: str) -> int:
    """
    快速估算目录中的文件数量（只扫描第一层）
    """
    try:
        with os.scandir(directory) as entries:
            return sum(1 for entry in entries if entry.is_file())
    except (PermissionError, OSError):
        return 0


def scan_directory_lightweight(directory: str, pkl_suffix: str = '.pkl') -> List[str]:
    """
    轻量级目录扫描 - 只处理单个目录，不递归
    返回该目录下符合条件的PKL文件（JPG数量=3）
    """
    try:
        with os.scandir(directory) as entries:
            pkl_files = []
            jpg_count = 0
            
            for entry in entries:
                if entry.is_file():
                    name_lower = entry.name.lower()
                    if name_lower.endswith(pkl_suffix):
                        pkl_files.append(entry.path)
                    elif name_lower.endswith(('.jpg', '.jpeg')):
                        jpg_count += 1
            
            # 只返回JPG数量为3的目录中的PKL文件
            if jpg_count == 3:
                return pkl_files
            else:
                return []
                
    except (PermissionError, OSError):
        return []


def process_directory_group(directories: List[str], pkl_suffix: str = '.pkl') -> List[str]:
    """
    处理一组目录的全局函数（用于多进程）
    """
    all_valid_files = []
    for directory in directories:
        # 先处理当前目录
        valid_files = scan_directory_lightweight(directory, pkl_suffix)
        all_valid_files.extend(valid_files)
        
        # 然后处理子目录
        try:
            with os.scandir(directory) as entries:
                for entry in entries:
                    if entry.is_dir() and not entry.name.startswith('.'):
                        subdir_files = scan_directory_lightweight(entry.path, pkl_suffix)
                        all_valid_files.extend(subdir_files)
        except (PermissionError, OSError):
            continue
    
    return all_valid_files


def smart_workload_distribution(root_directory: str, max_workers: int) -> List[List[str]]:
    """
    智能工作负载分配 - 根据目录大小动态分组
    """
    try:
        with os.scandir(root_directory) as entries:
            subdirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]
    except (PermissionError, OSError):
        return []
    
    if not subdirs:
        return []
    
    # 快速估算各目录大小
    dir_sizes = []
    for subdir in subdirs:
        size = estimate_directory_size(subdir)
        dir_sizes.append((size, subdir))
    
    # 按大小排序（大目录优先）
    dir_sizes.sort(reverse=True)
    
    # 使用贪心算法分配工作负载
    workers = [[] for _ in range(max_workers)]
    worker_loads = [0] * max_workers
    
    for size, directory in dir_sizes:
        # 找到负载最轻的worker
        min_worker = worker_loads.index(min(worker_loads))
        workers[min_worker].append(directory)
        worker_loads[min_worker] += size
    
    # 过滤空的worker组
    return [group for group in workers if group]


def find_valid_files_scalable(root_directory: str, pkl_suffix: str = '.pkl', max_workers: int = None) -> List[str]:
    """
    大规模优化版文件搜索 - 智能负载均衡 + 分层并行
    
    Args:
        root_directory: 根目录路径
        pkl_suffix: PKL文件后缀
        max_workers: 最大进程数
    
    Returns:
        符合条件的PKL文件完整路径列表
    """
    if not os.path.exists(root_directory):
        return []
    
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数
    
    print(f"开始智能负载分配，使用 {max_workers} 个进程...")
    
    # 智能分配工作负载
    worker_groups = smart_workload_distribution(root_directory, max_workers)
    
    if not worker_groups:
        # 如果没有子目录，直接处理根目录
        return scan_directory_lightweight(root_directory, pkl_suffix)
    
    print(f"工作组分配完成：{len(worker_groups)} 个组，平均每组 {sum(len(g) for g in worker_groups) / len(worker_groups):.1f} 个目录")
    
    all_valid_files = []
    
    # 多进程并行处理
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交任务
        future_to_group = {
            executor.submit(process_directory_group, group, pkl_suffix): i 
            for i, group in enumerate(worker_groups)
        }
        
        # 收集结果
        completed = 0
        for future in as_completed(future_to_group):
            try:
                result = future.result()
                all_valid_files.extend(result)
                completed += 1
                print(f"进度: {completed}/{len(worker_groups)} 组完成")
            except Exception as e:
                group_id = future_to_group[future]
                print(f"处理第 {group_id} 组时出错: {e}")
    
    return all_valid_files


class VideoDataScalable:
    def __init__(self, val_rate, suffix='pkl', dataset_dir='datasets'):
        self.suffix = suffix
        self.val_rate = val_rate
        self.dataset_dir = dataset_dir

    def convert_dataset_stream(self, label_name: int, valid_pkl_iter: List[str], 
                              txts_path_tup: Tuple, scl_name: str = 'Scl') -> int:
        """
        流式处理版本：边处理边写入，减少内存占用
        """
        all_txt_path, train_txt_path, val_txt_path = txts_path_tup
        
        # 将列表转换为索引集合以优化查找
        all_num = len(valid_pkl_iter)
        if all_num == 0:
            return 0
            
        val_indices = set(random.sample(range(all_num), int(self.val_rate * all_num)))
        
        processed = 0
        
        with open(all_txt_path, "a") as all_txt, \
             open(train_txt_path, "a") as train_txt, \
             open(val_txt_path, "a") as val_txt:
            
            for idx, vid_pth in enumerate(valid_pkl_iter):
                vid_pth = str(vid_pth)
                all_txt.write(f"{vid_pth} {label_name}\n")
                
                if idx in val_indices:
                    val_txt.write(f"{vid_pth} {label_name}\n")
                else:
                    train_txt.write(f"{vid_pth} {label_name}\n")
                
                processed += 1
        
        return processed

    def videos_files_convert_scalable(self, data_files_path: str, label_list: List[str], 
                                    test_mod: bool = False, default_label: int = None):
        """
        大规模优化版本的文件转换方法
        """
        # 设置输出路径
        if self.dataset_dir == "Fusion_datasets":
            dataset_txt_path = Path(data_files_path).parent / self.dataset_dir
        else:
            dataset_txt_path = Path(self.dataset_dir)
        dataset_txt_path.mkdir(exist_ok=True)

        # 确定文件名
        if test_mod:
            txt_names = ["test_all_label.txt", "test_train_label.txt", "test_val_label.txt"]
        else:
            txt_names = ["all_label.txt", "train_label.txt", "val_label.txt"]

        txts_path_tup = tuple(dataset_txt_path / name for name in txt_names)
        
        # 清空文件
        for path in txts_path_tup:
            path.write_text("")

        if default_label is not None:
            # 测试模式
            total_processed = 0
            for scl_dir in Path(data_files_path).glob('*'):
                if not scl_dir.is_dir():
                    continue
                
                print(f"\n处理目录: {scl_dir.name}")
                start_time = time.perf_counter()
                
                valid_pkl_list = find_valid_files_scalable(str(scl_dir), f'.{self.suffix}')
                
                scan_time = time.perf_counter() - start_time
                print(f"扫描完成: {len(valid_pkl_list)} 个有效文件，耗时 {scan_time:.2f}s")
                
                processed = self.convert_dataset_stream(default_label, valid_pkl_list, txts_path_tup, scl_dir.name)
                total_processed += processed
                
            print(f"\n测试模式完成，总处理 {total_processed} 个文件")
        else:
            # 训练模式
            total_files = 0
            total_time = 0
            
            for i, label_name in enumerate(label_list):
                print(f"\n=== 处理标签 {i}: {label_name} ===")
                video_files_path = Path(data_files_path) / label_name
                
                if not video_files_path.exists():
                    print(f"⚠️  路径不存在: {video_files_path}")
                    continue
                
                scls_lst = [d for d in video_files_path.iterdir() if d.is_dir()]
                print(f"发现 {len(scls_lst)} 个子目录")
                
                label_start_time = time.perf_counter()
                label_total_files = 0
                
                for scl_dir in scls_lst:
                    print(f"\n  处理子目录: {scl_dir.name}")
                    start_time = time.perf_counter()
                    
                    # 使用大规模优化的扫描
                    valid_pkl_list = find_valid_files_scalable(str(scl_dir), f'.{self.suffix}')
                    
                    scan_time = time.perf_counter() - start_time
                    print(f"    扫描: {len(valid_pkl_list)} 个有效文件，耗时 {scan_time:.2f}s")
                    
                    # 流式处理
                    processed = self.convert_dataset_stream(i, valid_pkl_list, txts_path_tup, scl_dir.name)
                    label_total_files += processed
                
                label_end_time = time.perf_counter()
                label_time = label_end_time - label_start_time
                
                print(f"\n📊 标签 '{label_name}' 统计:")
                print(f"   文件数: {label_total_files}")
                print(f"   耗时: {label_time:.2f}s")
                print(f"   速度: {label_total_files/label_time:.1f} 文件/秒")
                
                total_files += label_total_files
                total_time += label_time
            
            print(f"\n🎉 全部完成!")
            print(f"📈 总统计:")
            print(f"   总文件数: {total_files}")
            print(f"   总耗时: {total_time:.2f}s")
            print(f"   平均速度: {total_files/total_time:.1f} 文件/秒")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(
        prog='训练-验证标签生成 (大规模优化版)', 
        description='针对2W+大数据集优化的版本：智能负载均衡 + 分层并行'
    )
    parser.add_argument('--data_pth', type=str,
                        default="/media/pyl/WD_Blue_1T/All_proj/classify_cheat/pose_rgb", 
                        help='训练集标签来源')
    parser.add_argument('--label_name', default="labels.txt", 
                        help='标签名文件')
    parser.add_argument('--val_rate', default=0.15, 
                        help='验证集比例')
    parser.add_argument('--dataset_dir', default='Fusion_datasets', 
                        help='输出目录')
    parser.add_argument('--TestMod', type=str, default=False, 
                        help='测试集模式')
    parser.add_argument('--default_label', default=None, 
                        help='测试模式默认标签')
    parser.add_argument('--max_workers', type=int, default=None,
                        help='最大进程数 (默认自动检测)')

    opt = parser.parse_args()

    # 参数处理
    opt.val_rate = float(opt.val_rate)
    if isinstance(opt.TestMod, str):
        opt.TestMod = opt.TestMod.lower() == 'true'

    if opt.TestMod:
        opt.val_rate = 1.0
        label_pth = "/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/labels.txt"
    else:
        label_pth = Path(opt.data_pth) / opt.label_name

    # 显示配置
    print("🚀 === 大规模优化版标签生成脚本 ===")
    print(f"📁 数据路径: {opt.data_pth}")
    print(f"📊 验证集比例: {opt.val_rate}")
    print(f"🔧 CPU核心数: {mp.cpu_count()}")
    print(f"⚡ 最大进程数: {opt.max_workers or min(mp.cpu_count(), 8)}")
    
    # 读取标签
    labels_lst = read_labels(label_pth)
    print(f"🏷️  标签列表: {labels_lst}")

    # 开始处理
    video_data = VideoDataScalable(val_rate=opt.val_rate, dataset_dir=opt.dataset_dir)
    
    total_start = time.perf_counter()
    video_data.videos_files_convert_scalable(
        opt.data_pth, labels_lst, opt.TestMod, opt.default_label
    )
    total_end = time.perf_counter()
    
    print(f"\n✅ === 全部完成 ===")
    print(f"⏱️  总耗时: {total_end - total_start:.2f} 秒")
    print(f"💾 输出目录: {Path(opt.dataset_dir) if opt.dataset_dir != 'Fusion_datasets' else Path(opt.data_pth).parent / 'Fusion_datasets'}")
