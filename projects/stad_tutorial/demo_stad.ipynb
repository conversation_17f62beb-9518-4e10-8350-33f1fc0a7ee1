#%% md
<a href="https://colab.research.google.com/github/open-mmlab/mmaction2/projects/stad_tutorial/demo_stad.ipynb" target="_parent"><img src="https://colab.research.google.com/assets/colab-badge.svg" alt="Open In Colab"/></a>
#%% md
# Spatio-temporal action detection with MMAction2
Welcome to MMAction2! This is a tutorial on how to use MMAction2 for spatio-temporal action detection. In this tutorial, we will use the MultiSports dataset as an example, and provide a complete step-by-step guide for spatio-temporal action detection, including
- Prepare spatio-temporal action detection dataset
- Train detection model
- Prepare AVA format dataset
- Train spatio-temporal action detection model

#%% md
## 0. Install MMAction2 and MMDetection
#%%
%pip install -U openmim
!mim install mmengine
!mim install mmcv
!mim install mmdet

!git clone https://github.com/open-mmlab/mmaction2.git

%cd mmaction2
%pip install -v -e .
%cd projects/stad_tutorial
#%% md
## 1. Prepare spatio-temporal action detection dataset

Similar to detection tasks that require bounding box annotations, spatio-temporal action detection tasks require temporal and spatial localization, so more complex tube annotations are required. Taking the MultiSports dataset as an example, the `gttubes` field provides all the target action annotations in the video, and the following is an annotation fragment:

```
    'gttubes': {
        'aerobic_gymnastics/v_aqMgwPExjD0_c001': # video_key
            {
                10: # label index
                    [
                        array([[ 377.,  904.,  316., 1016.,  584.], # 1st tube of class 10
                               [ 378.,  882.,  315., 1016.,  579.], # shape (n, 5): n frames，each annotation includes (frame idx，x1，y1, x2, y2)
                               ...
                               [ 398.,  861.,  304.,  954.,  549.]], dtype=float32)，

                        array([[ 399.,  881.,  308.,  955.,  542.], # 2nd tube of class 10
                               [ 400.,  862.,  303.,  988.,  539.],
                               [ 401.,  853.,  292., 1000.,  535.],
                               ...])
                        ...

                    ] ,
                9: # label index
                    [
                        array(...), # 1st tube of class 9
                        array(...), # 2nd tube of class 9
                        ...
                    ]
                ...
            }
    }
```

The annotation file also needs to provide other field information, and the complete ground truth file includes the following information:

```
{
    'labels':  # label list
        ['aerobic push up', 'aerobic explosive push up', ...],
    'train_videos':  # training video list
        [
            [
                'aerobic_gymnastics/v_aqMgwPExjD0_c001',
                'aerobic_gymnastics/v_yaKOumdXwbU_c019',
                ...
            ]
        ]
    'test_videos':  # test video list
        [
            [
                'aerobic_gymnastics/v_crsi07chcV8_c004',
                'aerobic_gymnastics/v_dFYr67eNMwA_c005',
                ...
            ]
        ]
    'n_frames':  # dict provides frame number of each video
        {
            'aerobic_gymnastics/v_crsi07chcV8_c004': 725,
            'aerobic_gymnastics/v_dFYr67eNMwA_c005': 750,
            ...
        }
    'resolution':  # dict provides resolution of each video
        {
            'aerobic_gymnastics/v_crsi07chcV8_c004': (720, 1280),
            'aerobic_gymnastics/v_dFYr67eNMwA_c005': (720, 1280),
            ...
        }
    'gt_tubes':  # dict provides bouding boxes of each tube
        {
            ... # refer to above description
        }
}
```

The subsequent experiments are based on MultiSports-tiny, we extracted a small number of videos from MultiSports for demonstration purposes.
#%%
# Download dataset
!wget -P data -c https://download.openmmlab.com/mmaction/v1.0/projects/stad_tutorial/multisports-tiny.tar
!tar -xvf data/multisports-tiny.tar --strip 1 -C data
!apt-get -q install tree
!tree data
#%% md
## 2. Train detection model

In the SlowOnly + Det paradigm, we need to train a human detector first, and then predict actions based on the detection results. In this section, we train a detection model based on the annotation format in the previous section and the MMDetection algorithm library.

### 2.1 Build detection dataset annotation (COCO format)

Based on the annotation information of the spatio-temporal action detection dataset, we can build a COCO format detection dataset for training the detection model. We provide a script to convert the MultiSports format annotation, if you need to convert from other formats, you can refer to the [custom dataset](https://mmdetection.readthedocs.io/zh_CN/latest/advanced_guides/customize_dataset.html) document provided by MMDetection.
#%%
!python tools/generate_mmdet_anno.py data/multisports/annotations/multisports_GT.pkl data/multisports/annotations/multisports_det_anno.json
!tree data/multisports/annotations
#%%
!python tools/generate_rgb.py
#%% md
### 2.2 Modify config file

We use faster-rcnn_x101-64x4d_fpn_1x_coco as the base configuration, and make the following modifications to train on the MultiSports dataset. The following parts need to be modified:
- Number of model categories
- Learning rate adjustment strategy
- Optimizer configuration
- Dataset/annotation file path
- Evaluator configuration
- Pre-trained model

For more detailed tutorials, please refer to the [prepare configuration file](https://mmdetection.readthedocs.io/zh_CN/latest/user_guides/train.html#id9) document provided by MMDetection.
#%%
!cat configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py
#%% md
### 2.3 Train detection model
#%% md
By using MIM, you can directly train MMDetection models in the current directory. Here is the simplest example of training on a single GPU. For more training commands, please refer to the MIM [tutorial](https://github.com/open-mmlab/mim#command).
#%%
!mim train mmdet configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py \
    --work-dir work_dirs/det_model
#%% md
### 2.4 Generating Proposal BBoxes

During the training of the spatiotemporal action detection model, we need to rely on proposals generated by the detection model, rather than annotated detection boxes. Therefore, we need to use a trained detection model to perform inference on the entire dataset and convert the resulting proposals into the required format for subsequent training.

#### 2.4.1 Converting the Dataset to Coco Format

We provide a script to convert the MultiSports dataset into an annotation format without ground truth, which is used for inference.
#%%
!echo 'person' > data/multisports/annotations/label_map.txt
!python tools/images2coco.py \
        data/multisports/rawframes \
        data/multisports/annotations/label_map.txt \
        ms_infer_anno.json
#%% md
#### 2.4.2 Inference for Generating Proposal Files

#%% md
The inference of MMDetection models is also based on MIM. For more testing commands, please refer to the MIM [tutorial](GitHub - open-mmlab/mim: MIM Installs OpenMMLab Packages).

After the inference is completed, the results will be saved in 'data/multisports/ms_proposals.pkl'.
#%%
!mim test mmdet configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py \
    --checkpoint work_dirs/det_model/epoch_2.pth \
    --out data/multisports/annotations/ms_det_proposals.pkl
#%% md
## 3. Training the Spatio-temporal Action Detection Model
The provided annotation files and the proposal files generated by MMDetection need to be converted to the required format for training the spatiotemporal action detection model. We have provided relevant script to generate the specified format.
#%%
# Convert annotation files
!python ../../tools/data/multisports/parse_anno.py

# Convert proposal files
!python tools/convert_proposals.py

!tree data/multisports/annotations
#%% md
### 3.2 Training the Spatio-temporal Action Detection Model

MMAction2 already supports training on the MultiSports dataset. You just need to modify the path to the proposal file. For detailed configurations, please refer to the [config](configs/slowonly_k400_multisports.py) file. Since the training data is limited, the configuration uses a pre-trained model trained on the complete MultiSports dataset. When training with a custom dataset, you don't need to specify the `load_from` configuration.
#%%
# Train the model using MIM
!mim train mmaction2 configs/slowonly_k400_multisports.py \
    --work-dir work_dirs/stad_model/
#%% md
## 4. Inferring the Spatiotemporal Action Detection Model

After training the detection model and the spatiotemporal action detection model, we can use the spatiotemporal action detection demo for inference and visualize the model's performance.

Since the tutorial uses a limited training dataset, the model's performance is not optimal, so a pre-trained model is used for visualization.
#%%
!python ../../demo/demo_spatiotemporal_det.py \
    data/multisports/test/aerobic_gymnastics/v_7G_IpU0FxLU_c001.mp4 \
    data/demo_spatiotemporal_det.mp4 \
    --config configs/slowonly_k400_multisports.py \
    --checkpoint https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb_20230320-a1ca5e76.pth \
    --det-config configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py \
    --det-checkpoint work_dirs/det_model/epoch_2.pth \
    --det-score-thr 0.85 \
    --action-score-thr 0.8 \
    --label-map ../../tools/data/multisports/label_map.txt \
    --predict-stepsize 8 \
    --output-stepsize 1 \
    --output-fps 24
#%%
# Show Video
import moviepy.editor
moviepy.editor.ipython_display("data/demo_spatiotemporal_det.mp4")