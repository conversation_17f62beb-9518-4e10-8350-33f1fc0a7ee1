#%% md
<a href="https://colab.research.google.com/github/open-mmlab/mmaction2/projects/stad_tutorial/demo_stad_zh_CN.ipynb" target="_parent"><img src="https://colab.research.google.com/assets/colab-badge.svg" alt="Open In Colab"/></a>
#%% md
# 基于 MMAction2 进行时空行为检测任务
欢迎使用 MMAction2! 这是一篇关于如何使用 MMAction2 进行时空行为检测的教程。在此教程中，我们会以 MultiSports 数据集为例，提供时空行为检测的完整步骤教程，包括
- 准备时空行为检测数据集
- 训练检测模型
- 准备 AVA 格式的数据集
- 训练时空行为检测模型

#%% md
## 0. 安装 MMAction2 和 MMDetection
#%%
%pip install -U openmim
!mim install mmengine
!mim install mmcv
!mim install mmdet

!git clone https://github.com/open-mmlab/mmaction2.git

%cd mmaction2
%pip install -v -e .
%cd projects/stad_tutorial
#%% md
## 1. 准备时空行为检测数据集

类似于检测任务需要提供检测框标注，时空行为检测任务需要对时间和空间同时定位，所以需要提供更复杂的 tube 标注。以 MultiSports 数据集的标注为例，`gttubes` 字段提供了视频中所有的目标动作标注，以下为一个标注片段：

```
    'gttubes': {
        'aerobic_gymnastics/v_aqMgwPExjD0_c001': # video_key
            {
                10: # 类别标号
                    [
                        array([[ 377.,  904.,  316., 1016.,  584.], # 类别 10 的第 1 个 tube,
                               [ 378.,  882.,  315., 1016.,  579.], # shape (n, 5): 表示 n 帧，每帧标注中包括 (帧号，x1，y1, x2, y2)
                               ...
                               [ 398.,  861.,  304.,  954.,  549.]], dtype=float32)，

                        array([[ 399.,  881.,  308.,  955.,  542.], # 类别 10 的第 2 个 tube
                               [ 400.,  862.,  303.,  988.,  539.],
                               [ 401.,  853.,  292., 1000.,  535.],
                               ...])
                        ...

                    ] ,
                9: # 类别标号
                    [
                        array(...), # 类别 9 的第 1 个 tube
                        array(...), # 类别 9 的第 2 个 tube
                        ...
                    ]
                ...
            }
    }
```

标注文件中还需要提供其他字段的信息，完整的真值文件包括以下信息：
```
{
    'labels':  # 标签列表
        ['aerobic push up', 'aerobic explosive push up', ...],
    'train_videos':  # 训练视频列表
        [
            [
                'aerobic_gymnastics/v_aqMgwPExjD0_c001',
                'aerobic_gymnastics/v_yaKOumdXwbU_c019',
                ...
            ]
        ]
    'test_videos':  # 测试视频列表
        [
            [
                'aerobic_gymnastics/v_crsi07chcV8_c004',
                'aerobic_gymnastics/v_dFYr67eNMwA_c005',
                ...
            ]
        ]
    'n_frames':  # dict 文件，提供各个视频的帧数信息
        {
            'aerobic_gymnastics/v_crsi07chcV8_c004': 725,
            'aerobic_gymnastics/v_dFYr67eNMwA_c005': 750,
            ...
        }
    'resolution':  # dict 文件，提供各个视频的分辨率信息
        {
            'aerobic_gymnastics/v_crsi07chcV8_c004': (720, 1280),
            'aerobic_gymnastics/v_dFYr67eNMwA_c005': (720, 1280),
            ...
        }
    'gt_tubes':  # dict 文件，提供 tube 的检测框信息
        {
            ... # 格式参考上述说明
        }
}
```
后续的实验基于 MultiSports-tiny 进行，我们从 MultiSports 中抽取了少量视频，用于演示整个流程。
#%%
# 下载数据集
!wget -P data -c https://download.openmmlab.com/mmaction/v1.0/projects/stad_tutorial/multisports-tiny.tar
!tar -xvf data/multisports-tiny.tar --strip 1 -C data
!apt-get -q install tree
!tree data
#%% md
## 2. 训练检测模型

在 SlowOnly + Det 的范式中，需要先训练人体检测器，再基于检测结果来预测行为。这一节中，我们基于上一节中的标注格式和 MMDetection 算法库训练检测模型。

### 2.1 构建检测数据集标注（COCO 格式）

基于时空行为检测数据集的标注信息，我们可以构建一个 COCO 格式的检测数据集，用于训练检测模型。我们提供了一个工具脚本对 MultiSports 格式的标注进行转换，如果需要基于其他格式转换，可以参考 MMDetection 提供的[自定义数据集](https://mmdetection.readthedocs.io/zh_CN/latest/advanced_guides/customize_dataset.html)文档。
#%%
!python tools/generate_mmdet_anno.py data/multisports/annotations/multisports_GT.pkl data/multisports/annotations/multisports_det_anno.json
!tree data/multisports/annotations
#%%
!python tools/generate_rgb.py
#%% md
### 2.2 修改 config 文件

我们以 faster-rcnn_x101-64x4d_fpn_1x_coco 为基础配置，做如下修改，在 MultiSports 数据集上进行训练。需要修改以下几个部分：
- 模型的类别数量
- 学习率调整策略
- 优化器配置
- 数据集/标注文件路径
- 评测器配置
- 预训练模型

更详细的教程可以参考 MMDetection 提供的[准备配置文件](https://mmdetection.readthedocs.io/zh_CN/latest/user_guides/train.html#id9)文档。
#%%
!cat configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py
#%% md
### 2.3 训练检测模型
#%% md
利用 MIM 可以在当前路径直接训练 MMDetection 模型，这里提供最简单的单卡训练示例，更多训练命令可以参考 MIM [教程](https://github.com/open-mmlab/mim#command)。
#%%
!mim train mmdet configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py \
    --work-dir work_dirs/det_model
#%% md
### 2.4 生成 proposal bboxes

在时空行为检测模型训练时，需要基于检测模型推理得到的 proposal，而不能基于标注的检测框。因此我们需要利用训练好的检测模型对整个数据集进行推理，得到 proposal 后转换为需要的格式，用于后续训练。

#### 2.4.1 将数据集转换为 Coco 格式

我们提供了脚本将 MultiSports 数据集转换成没有真值的标注格式，用于推理。
#%%
!echo 'person' > data/multisports/annotations/label_map.txt
!python tools/images2coco.py \
        data/multisports/rawframes \
        data/multisports/annotations/label_map.txt \
        ms_infer_anno.json
#%% md
#### 2.4.2 推理生成 proposal file
#%% md
MMDetection 模型的推理同样基于 MIM，更多测试命令请参考 MIM [教程](https://github.com/open-mmlab/mim#command)。

推理完成后，会将推理结果保存在 'data/multisports/ms_proposals.pkl'。
#%%
!mim test mmdet configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py \
    --checkpoint work_dirs/det_model/epoch_2.pth \
    --out data/multisports/annotations/ms_det_proposals.pkl
#%% md
## 3. 训练时空行为检测模型

### 3.1 转换标注文件以及 proposal 文件

MultiSports 数据集提供的标注文件，以及 MMDetection 推理生成的 proposal 都需要进行格式转换，才能用于时空行为检测模型的训练。我们已经提供了相关的脚本工具，执行后即可生成指定格式
#%%
# 转换 anno 文件
!python ../../tools/data/multisports/parse_anno.py

# 转换 proposal 文件
!python tools/convert_proposals.py

!tree data/multisports/annotations
#%% md
### 3.2 训练时空行为检测模型

MMAction2 中已经支持训练 MultiSports 数据集，这里只需要修改 proposal 文件的路径即可, 详细配置可以参考 [config](configs/slowonly_k400_multisports.py) 文件。由于训练数据较少，配置中将在完整 MultiSports 数据集上训练得到的模型作为预训练模型，使用自定义数据集训练时不需要指定 `load_from` 配置。
#%%
# 使用 MIM 训练模型
!mim train mmaction2 configs/slowonly_k400_multisports.py \
    --work-dir work_dirs/stad_model/
#%% md
## 4. 时空行为检测模型推理

训练得到检测模型和时空行为检测模型后，我们可以利用时空行为检测 demo 进行推理，可视化模型效果。

由于 tutorial 中使用的训练数据较少，模型性能较差，所以可视化时使用预先训练好的模型。
#%% md
###
#%%
!python ../../demo/demo_spatiotemporal_det.py \
    data/multisports/test/aerobic_gymnastics/v_7G_IpU0FxLU_c001.mp4 \
    data/demo_spatiotemporal_det.mp4 \
    --config configs/slowonly_k400_multisports.py \
    --checkpoint https://download.openmmlab.com/mmaction/v1.0/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb_20230320-a1ca5e76.pth \
    --det-config configs/faster-rcnn_r50-caffe_fpn_ms-1x_coco_ms_person.py \
    --det-checkpoint work_dirs/det_model/epoch_2.pth \
    --det-score-thr 0.85 \
    --action-score-thr 0.8 \
    --label-map ../../tools/data/multisports/label_map.txt \
    --predict-stepsize 8 \
    --output-stepsize 1 \
    --output-fps 24
#%%
# Show Video
import moviepy.editor
moviepy.editor.ipython_display("data/demo_spatiotemporal_det.mp4")