# Welcome to Projects of MMAction2

In this folder, we welcome all contributions of deep-learning video understanding models from the community.

Here, these requirements, e.g., code standards, are not as strict as in the core package. Thus, developers from the community can implement their algorithms much more easily and efficiently in MMAction2. We appreciate all contributions from the community to make MMAction2 greater.

Here is an [example project](./example_project) about how to add your algorithms easily.

We also provide some documentation listed below:

- [Contribution Guide](https://mmaction2.readthedocs.io/en/latest/get_started/contribution_guide.html)

  The guides for new contributors about how to add your projects to MMAction2.

- [Discussions](https://github.com/open-mmlab/mmaction2/discussions)

  Welcome to start a discussion!
