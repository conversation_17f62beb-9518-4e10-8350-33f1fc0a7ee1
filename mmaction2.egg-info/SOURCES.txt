MANIFEST.in
README.md
setup.cfg
setup.py
mmaction/__init__.py
mmaction/registry.py
mmaction/version.py
mmaction/.mim/dataset-index.yml
mmaction/.mim/model-index.yml
mmaction/.mim/configs/_base_/default_runtime.py
mmaction/.mim/configs/_base_/models/audioonly_r50.py
mmaction/.mim/configs/_base_/models/bmn_400x100.py
mmaction/.mim/configs/_base_/models/bsn_pem.py
mmaction/.mim/configs/_base_/models/bsn_tem.py
mmaction/.mim/configs/_base_/models/c2d_r50.py
mmaction/.mim/configs/_base_/models/c3d_sports1m_pretrained.py
mmaction/.mim/configs/_base_/models/i3d_r50.py
mmaction/.mim/configs/_base_/models/ircsn_r152.py
mmaction/.mim/configs/_base_/models/mvit_small.py
mmaction/.mim/configs/_base_/models/r2plus1d_r34.py
mmaction/.mim/configs/_base_/models/slowfast_r50.py
mmaction/.mim/configs/_base_/models/slowonly_r50.py
mmaction/.mim/configs/_base_/models/swin_tiny.py
mmaction/.mim/configs/_base_/models/tanet_r50.py
mmaction/.mim/configs/_base_/models/tin_r50.py
mmaction/.mim/configs/_base_/models/tpn_slowonly_r50.py
mmaction/.mim/configs/_base_/models/tpn_tsm_r50.py
mmaction/.mim/configs/_base_/models/trn_r50.py
mmaction/.mim/configs/_base_/models/tsm_mobilenet_v2.py
mmaction/.mim/configs/_base_/models/tsm_mobileone_s4.py
mmaction/.mim/configs/_base_/models/tsm_r50.py
mmaction/.mim/configs/_base_/models/tsn_mobileone_s0.py
mmaction/.mim/configs/_base_/models/tsn_r50.py
mmaction/.mim/configs/_base_/models/x3d.py
mmaction/.mim/configs/_base_/schedules/adam_20e.py
mmaction/.mim/configs/_base_/schedules/sgd_100e.py
mmaction/.mim/configs/_base_/schedules/sgd_150e_warmup.py
mmaction/.mim/configs/_base_/schedules/sgd_50e.py
mmaction/.mim/configs/_base_/schedules/sgd_tsm_100e.py
mmaction/.mim/configs/_base_/schedules/sgd_tsm_50e.py
mmaction/.mim/configs/_base_/schedules/sgd_tsm_mobilenet_v2_100e.py
mmaction/.mim/configs/_base_/schedules/sgd_tsm_mobilenet_v2_50e.py
mmaction/.mim/configs/detection/acrn/metafile.yml
mmaction/.mim/configs/detection/acrn/slowfast-acrn_kinetics400-pretrained-r50_8xb8-8x8x1-cosine-10e_ava21-rgb.py
mmaction/.mim/configs/detection/acrn/slowfast-acrn_kinetics400-pretrained-r50_8xb8-8x8x1-cosine-10e_ava22-rgb.py
mmaction/.mim/configs/detection/lfb/metafile.yml
mmaction/.mim/configs/detection/lfb/slowonly-lfb-infer_r50_ava21-rgb.py
mmaction/.mim/configs/detection/lfb/slowonly-lfb-max_kinetics400-pretrained-r50_8xb12-4x16x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/lfb/slowonly-lfb-nl_kinetics400-pretrained-r50_8xb12-4x16x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/lfb/slowonly-lfb_ava-pretrained-r50_infer-4x16x1_ava21-rgb.py
mmaction/.mim/configs/detection/slowfast/metafile.yml
mmaction/.mim/configs/detection/slowfast/slowfast_kinetics400-pretrained-r50-context_8xb16-4x16x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/slowfast/slowfast_kinetics400-pretrained-r50-temporal-max_8xb6-8x8x1-cosine-10e_ava22-rgb.py
mmaction/.mim/configs/detection/slowfast/slowfast_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/slowfast/slowfast_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb.py
mmaction/.mim/configs/detection/slowfast/slowfast_kinetics400-pretrained-r50_8xb6-8x8x1-cosine-10e_ava22-rgb.py
mmaction/.mim/configs/detection/slowfast/slowfast_kinetics400-pretrained-r50_8xb8-8x8x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/slowfast/slowfast_r50-k400-pre-temporal-max-focal-alpha3-gamma1_8xb6-8x8x1-cosine-10e_ava22-rgb.py
mmaction/.mim/configs/detection/slowonly/metafile.yml
mmaction/.mim/configs/detection/slowonly/slowonly_k400-pre-r50_8xb8-4x16x1-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k400-pre-r50_8xb8-8x8x1-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k700-pre-r50-context-temporal-max-nl-head_8xb8-8x8x1-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k700-pre-r50-context-temporal-max-nl-head_8xb8-8x8x1-focal-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k700-pre-r50-context-temporal-max_8xb8-8x8x1-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k700-pre-r50-context_8xb8-8x8x1-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k700-pre-r50_8xb8-16x4x1-10e-tricks_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k700-pre-r50_8xb8-4x16x1-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_k700-pre-r50_8xb8-8x8x1-10e_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_kinetics400-pretrained-r101_8xb16-8x8x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-4x16x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_kinetics400-pretrained-r50-nl_8xb16-8x8x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_kinetics400-pretrained-r50_8xb16-4x16x1-8e_multisports-rgb.py
mmaction/.mim/configs/detection/slowonly/slowonly_kinetics700-pretrained-r50_8xb16-4x16x1-20e_ava21-rgb.py
mmaction/.mim/configs/detection/videomae/metafile.yml
mmaction/.mim/configs/detection/videomae/vit-base-p16_videomae-k400-pre_8xb8-16x4x1-20e-adamw_ava-kinetics-rgb.py
mmaction/.mim/configs/detection/videomae/vit-large-p16_videomae-k400-pre_8xb8-16x4x1-20e-adamw_ava-kinetics-rgb.py
mmaction/.mim/configs/localization/bmn/bmn_2xb8-2048x100-9e_activitynet-slowonly-k700-feature.py
mmaction/.mim/configs/localization/bmn/bmn_2xb8-400x100-9e_activitynet-feature.py
mmaction/.mim/configs/localization/bmn/metafile.yml
mmaction/.mim/configs/localization/bsn/bsn_pem_1xb16-2048x100-20e_activitynet-slowonly-k700-feature.py
mmaction/.mim/configs/localization/bsn/bsn_pem_1xb16-400x100-20e_activitynet-feature.py
mmaction/.mim/configs/localization/bsn/bsn_pgm_2048x100_activitynet-slowonly-k700-feature.py
mmaction/.mim/configs/localization/bsn/bsn_pgm_400x100_activitynet-feature.py
mmaction/.mim/configs/localization/bsn/bsn_tem_1xb16-2048x100-20e_activitynet-k700-feature.py
mmaction/.mim/configs/localization/bsn/bsn_tem_1xb16-400x100-20e_activitynet-feature.py
mmaction/.mim/configs/localization/bsn/metafile.yml
mmaction/.mim/configs/localization/drn/drn_2xb16-4096-10e_c3d-feature_first.py
mmaction/.mim/configs/localization/drn/drn_2xb16-4096-10e_c3d-feature_second.py
mmaction/.mim/configs/localization/drn/drn_2xb16-4096-10e_c3d-feature_third.py
mmaction/.mim/configs/localization/drn/metafile.yml
mmaction/.mim/configs/localization/tcanet/metafile.yml
mmaction/.mim/configs/localization/tcanet/tcanet_2xb8-700x100-9e_hacs-feature.py
mmaction/.mim/configs/multimodal/vindlu/metafile.yml
mmaction/.mim/configs/multimodal/vindlu/vindlu_beit-base_8x16_retrieval_msrvtt-9k.py
mmaction/.mim/configs/multimodal/vindlu/vindlu_beit-base_8x8_vqa_msrvtt-qa.py
mmaction/.mim/configs/multimodal/vindlu/vindlu_beit-base_vqa-mc_msrvtt-mc.py
mmaction/.mim/configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py
mmaction/.mim/configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_ResNetTSM512.py
mmaction/.mim/configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_focal.py
mmaction/.mim/configs/recognition/c2d/c2d_r101-in1k-pre-nopool_8xb32-8x8x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/c2d/c2d_r50-in1k-pre-nopool_8xb32-8x8x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/c2d/c2d_r50-in1k-pre_8xb32-16x4x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/c2d/c2d_r50-in1k-pre_8xb32-8x8x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/c2d/metafile.yml
mmaction/.mim/configs/recognition/c3d/c3d_sports1m-pretrained_8xb30-16x1x1-45e_ucf101-rgb.py
mmaction/.mim/configs/recognition/c3d/metafile.yml
mmaction/.mim/configs/recognition/csn/ipcsn_ig65m-pretrained-r152-bnfrozen_32x2x1-58e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/ipcsn_r152_32x2x1-180e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/ipcsn_sports1m-pretrained-r152-bnfrozen_32x2x1-58e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/ircsn_ig65m-pretrained-r152-bnfrozen_8xb12-32x2x1-58e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/ircsn_ig65m-pretrained-r152_8xb12-32x2x1-58e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/ircsn_ig65m-pretrained-r50-bnfrozen_8xb12-32x2x1-58e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/ircsn_r152_32x2x1-180e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/ircsn_sports1m-pretrained-r152-bnfrozen_32x2x1-58e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/csn/metafile.yml
mmaction/.mim/configs/recognition/i3d/i3d_imagenet-pretrained-r50-heavy_8xb8-32x2x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/i3d/i3d_imagenet-pretrained-r50-nl-dot-product_8xb8-32x2x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/i3d/i3d_imagenet-pretrained-r50-nl-embedded-gaussian_8xb8-32x2x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/i3d/i3d_imagenet-pretrained-r50-nl-gaussian_8xb8-32x2x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/i3d/i3d_imagenet-pretrained-r50_8xb8-32x2x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/i3d/i3d_imagenet-pretrained-r50_8xb8-dense-32x2x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/i3d/metafile.yml
mmaction/.mim/configs/recognition/mvit/metafile.yml
mmaction/.mim/configs/recognition/mvit/mvit-base-p244_32x3x1_kinetics400-rgb.py
mmaction/.mim/configs/recognition/mvit/mvit-base-p244_u32_sthv2-rgb.py
mmaction/.mim/configs/recognition/mvit/mvit-large-p244_40x3x1_kinetics400-rgb.py
mmaction/.mim/configs/recognition/mvit/mvit-large-p244_u40_sthv2-rgb.py
mmaction/.mim/configs/recognition/mvit/mvit-small-p244_32xb16-16x4x1-200e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/mvit/mvit-small-p244_k400-maskfeat-pre_8xb32-16x4x1-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/mvit/mvit-small-p244_k400-pre_16xb16-u16-100e_sthv2-rgb.py
mmaction/.mim/configs/recognition/omnisource/metafile.yml
mmaction/.mim/configs/recognition/omnisource/slowonly_r50_8xb16-8x8x1-256e_imagenet-kinetics400-rgb.py
mmaction/.mim/configs/recognition/r2plus1d/metafile.yml
mmaction/.mim/configs/recognition/r2plus1d/r2plus1d_r34_8xb8-32x2x1-180e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/r2plus1d/r2plus1d_r34_8xb8-8x8x1-180e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowfast/metafile.yml
mmaction/.mim/configs/recognition/slowfast/slowfast_r101-r50_32xb8-4x16x1-256e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowfast/slowfast_r101_8xb8-8x8x1-256e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowfast/slowfast_r50_8xb8-4x16x1-256e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowfast/slowfast_r50_8xb8-8x8x1-256e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowfast/slowfast_r50_8xb8-8x8x1-steplr-256e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowonly/metafile.yml
mmaction/.mim/configs/recognition/slowonly/slowonly_imagenet-pretrained-r50_16xb16-4x16x1-steplr-150e_kinetics700-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_imagenet-pretrained-r50_16xb16-8x8x1-steplr-150e_kinetics700-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_imagenet-pretrained-r50_32xb8-8x8x1-steplr-150e_kinetics710-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_imagenet-pretrained-r50_8xb16-8x8x1-steplr-150e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_r101_8xb16-8x8x1-196e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_r50-in1k-pre-nl-embedded-gaussian_8xb16-4x16x1-steplr-150e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_r50-in1k-pre-nl-embedded-gaussian_8xb16-8x8x1-steplr-150e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_r50_8xb16-16x4x1-256e_kinetics400-flow.py
mmaction/.mim/configs/recognition/slowonly/slowonly_r50_8xb16-4x16x1-256e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/slowonly/slowonly_r50_8xb16-8x8x1-256e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/swin/metafile.yml
mmaction/.mim/configs/recognition/swin/swin-base-p244-w877_in1k-pre_8xb8-amp-32x2x1-30e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/swin/swin-large-p244-w877_in22k-pre_16xb8-amp-32x2x1-30e_kinetics700-rgb.py
mmaction/.mim/configs/recognition/swin/swin-large-p244-w877_in22k-pre_8xb8-amp-32x2x1-30e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/swin/swin-small-p244-w877_in1k-pre_32xb4-amp-32x2x1-30e_kinetics710-rgb.py
mmaction/.mim/configs/recognition/swin/swin-small-p244-w877_in1k-pre_8xb8-amp-32x2x1-30e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/swin/swin-tiny-p244-w877_in1k-pre_8xb8-amp-32x2x1-30e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tanet/metafile.yml
mmaction/.mim/configs/recognition/tanet/tanet_imagenet-pretrained-r50_8xb6-1x1x16-50e_sthv1-rgb.py
mmaction/.mim/configs/recognition/tanet/tanet_imagenet-pretrained-r50_8xb8-1x1x8-50e_sthv1-rgb.py
mmaction/.mim/configs/recognition/tanet/tanet_imagenet-pretrained-r50_8xb8-dense-1x1x8-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/timesformer/metafile.yml
mmaction/.mim/configs/recognition/timesformer/timesformer_divST_8xb8-8x32x1-15e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/timesformer/timesformer_jointST_8xb8-8x32x1-15e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/timesformer/timesformer_spaceOnly_8xb8-8x32x1-15e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tin/metafile.yml
mmaction/.mim/configs/recognition/tin/tin_imagenet-pretrained-r50_8xb6-1x1x8-40e_sthv1-rgb.py
mmaction/.mim/configs/recognition/tin/tin_imagenet-pretrained-r50_8xb6-1x1x8-40e_sthv2-rgb.py
mmaction/.mim/configs/recognition/tin/tin_kinetics400-pretrained-tsm-r50_1x1x8-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tpn/metafile.yml
mmaction/.mim/configs/recognition/tpn/tpn-slowonly_imagenet-pretrained-r50_8xb8-8x8x1-150e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tpn/tpn-slowonly_r50_8xb8-8x8x1-150e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tpn/tpn-tsm_imagenet-pretrained-r50_8xb8-1x1x8-150e_sthv1-rgb.py
mmaction/.mim/configs/recognition/trn/metafile.yml
mmaction/.mim/configs/recognition/trn/trn_imagenet-pretrained-r50_8xb16-1x1x8-50e_sthv1-rgb.py
mmaction/.mim/configs/recognition/trn/trn_imagenet-pretrained-r50_8xb16-1x1x8-50e_sthv2-rgb.py
mmaction/.mim/configs/recognition/trn/trn_imagenet-standjump_vidUnderstand-r50_8xb16-1x1x8-50e_sthv1-rgb.py
mmaction/.mim/configs/recognition/tsm/metafile.yml
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-mobileone-s4_8xb16-1x1x16-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-mobileone-s4_deploy_8xb16-1x1x16-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r101_8xb16-1x1x8-50e_sthv2-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50-nl-dot-product_8xb16-1x1x8-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50-nl-embedded-gaussian_8xb16-1x1x8-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50-nl-gaussian_8xb16-1x1x8-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50_8xb16-1x1x16-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50_8xb16-1x1x16-50e_sthv2-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50_8xb16-1x1x8-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50_8xb16-1x1x8-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50_8xb16-1x1x8-50e_sthv2-rgb.py
mmaction/.mim/configs/recognition/tsm/tsm_imagenet-pretrained-r50_8xb16-dense-1x1x8-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/metafile.yml
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r101_8xb32-1x1x8-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x16-50e_sthv2-rgb.py
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x3-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x5-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-50e_sthv2-rgb.py
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-dense-1x1x5-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32_5x1x3-110e_kinetics400-flow.py
mmaction/.mim/configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-dense161_8xb32-1x1x3-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-mobileone-s4_8xb32-1x1x8-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-mobileone-s4_deploy_8xb32-1x1x8-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-rn101-32x4d_8xb32-1x1x3-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-swin-transformer_32xb8-1x1x8-50e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformer/metafile.yml
mmaction/.mim/configs/recognition/uniformer/uniformer-base_imagenet1k-pre_16x4x1_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformer/uniformer-base_imagenet1k-pre_32x4x1_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformer/uniformer-small_imagenet1k-pre_16x4x1_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/metafile.yml
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip-kinetics710-kinetics-k400-pre_16xb32-u8_mitv1-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip-kinetics710-pre_8xb32-u8_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip-kinetics710-pre_8xb32-u8_kinetics600-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip-kinetics710-pre_8xb32-u8_kinetics700-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip-pre_u8_kinetics710-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip_8xb32-u8_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip_8xb32-u8_kinetics700-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-base-p16-res224_clip_u8_kinetics710-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u16_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u16_kinetics600-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u16_kinetics700-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u32_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u32_kinetics600-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u32_kinetics700-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u8_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u8_kinetics600-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-kinetics710-pre_u8_kinetics700-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res224_clip-pre_u8_kinetics710-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res336_clip-kinetics710-pre_u32_kinetics400-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res336_clip-kinetics710-pre_u32_kinetics600-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res336_clip-kinetics710-pre_u32_kinetics700-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p14-res336_clip-pre_u8_kinetics710-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p16-res224_clip-kinetics710-kinetics-k400-pre_u8_mitv1-rgb.py
mmaction/.mim/configs/recognition/uniformerv2/uniformerv2-large-p16-res336_clip-kinetics710-kinetics-k400-pre_u8_mitv1-rgb.py
mmaction/.mim/configs/recognition/videomae/metafile.yml
mmaction/.mim/configs/recognition/videomae/vit-base-p16_videomae-k400-pre_16x4x1_kinetics-400.py
mmaction/.mim/configs/recognition/videomae/vit-large-p16_videomae-k400-pre_16x4x1_kinetics-400.py
mmaction/.mim/configs/recognition/videomaev2/metafile.yml
mmaction/.mim/configs/recognition/videomaev2/vit-base-p16_videomaev2-vit-g-dist-k710-pre_16x4x1_kinetics-400.py
mmaction/.mim/configs/recognition/videomaev2/vit-small-p16_videomaev2-vit-g-dist-k710-pre_16x4x1_kinetics-400.py
mmaction/.mim/configs/recognition/x3d/metafile.yml
mmaction/.mim/configs/recognition/x3d/x3d_m_16x5x1_facebook-kinetics400-rgb.py
mmaction/.mim/configs/recognition/x3d/x3d_s_13x6x1_facebook-kinetics400-rgb.py
mmaction/.mim/configs/recognition_audio/audioonly/audioonly_r50_8xb160-64x1x1-100e_kinetics400-audio-feature.py
mmaction/.mim/configs/recognition_audio/resnet/metafile.yml
mmaction/.mim/configs/recognition_audio/resnet/tsn_r18_8xb320-64x1x1-100e_kinetics400-audio-feature.py
mmaction/.mim/configs/retrieval/clip4clip/clip4clip_vit-base-p32-res224-clip-pre_8xb16-u12-5e_msrvtt-9k-rgb.py
mmaction/.mim/configs/retrieval/clip4clip/metafile.yml
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-bone-motion-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-bone-motion-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-bone-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-bone-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-joint-motion-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-joint-motion-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-joint-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/2s-agcn/2s-agcn_8xb16-joint-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/2s-agcn/metafile.yml
mmaction/.mim/configs/skeleton/posec3d/metafile.yml
mmaction/.mim/configs/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_hmdb51-split1-keypoint.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_ucf101-split1-keypoint.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-keypoint.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-limb.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-keypoint.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-limb.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_r50_8xb32-u48-240e_k400-keypoint.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_r50_sport-keypoint.py
mmaction/.mim/configs/skeleton/posec3d/slowonly_r50_sport-xsub-keypoint.py
mmaction/.mim/configs/skeleton/posec3d/rgbpose_conv3d/pose_only.py
mmaction/.mim/configs/skeleton/posec3d/rgbpose_conv3d/rgb_only.py
mmaction/.mim/configs/skeleton/posec3d/rgbpose_conv3d/rgbpose_conv3d.py
mmaction/.mim/configs/skeleton/stgcn/metafile.yml
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-motion-u100-80e_ntu120-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-motion-u100-80e_ntu120-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-motion-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-motion-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-bone-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-motion-u100-80e_ntu120-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-motion-u100-80e_ntu120-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-motion-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-motion-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-u100-80e_ntu120-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-u100-80e_ntu120-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcn/stgcn_8xb16-joint-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcnpp/metafile.yml
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-bone-motion-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-bone-motion-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-bone-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-bone-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-joint-motion-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-joint-motion-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-joint-u100-80e_ntu60-xsub-keypoint-2d.py
mmaction/.mim/configs/skeleton/stgcnpp/stgcnpp_8xb16-joint-u100-80e_ntu60-xsub-keypoint-3d.py
mmaction/.mim/tools/GPU_Monitor_TrainSitUp.sh
mmaction/.mim/tools/compare_training_scripts.py
mmaction/.mim/tools/detect_samples.sh
mmaction/.mim/tools/detect_testsets.sh
mmaction/.mim/tools/dist_test.sh
mmaction/.mim/tools/dist_train.sh
mmaction/.mim/tools/predict.py
mmaction/.mim/tools/slurm_test.sh
mmaction/.mim/tools/slurm_train.sh
mmaction/.mim/tools/test.py
mmaction/.mim/tools/test_focal_loss_components.py
mmaction/.mim/tools/train.py
mmaction/.mim/tools/train_focal.py
mmaction/.mim/tools/train_m2.py
mmaction/.mim/tools/train_sport.sh
mmaction/.mim/tools/analysis_tools/analyze_logs.py
mmaction/.mim/tools/analysis_tools/bench_processing.py
mmaction/.mim/tools/analysis_tools/benchmark.py
mmaction/.mim/tools/analysis_tools/check_videos.py
mmaction/.mim/tools/analysis_tools/confusion_matrix.py
mmaction/.mim/tools/analysis_tools/eval_metric.py
mmaction/.mim/tools/analysis_tools/get_flops.py
mmaction/.mim/tools/analysis_tools/print_config.py
mmaction/.mim/tools/analysis_tools/report_accuracy.py
mmaction/.mim/tools/analysis_tools/report_map.py
mmaction/.mim/tools/convert/convert_recognizer.py
mmaction/.mim/tools/convert/reparameterize_model.py
mmaction/.mim/tools/data/anno_txt2json.py
mmaction/.mim/tools/data/build_audio_features.py
mmaction/.mim/tools/data/build_file_list.py
mmaction/.mim/tools/data/build_rawframes.py
mmaction/.mim/tools/data/build_videos.py
mmaction/.mim/tools/data/denormalize_proposal_file.py
mmaction/.mim/tools/data/extract_audio.py
mmaction/.mim/tools/data/parse_file_list.py
mmaction/.mim/tools/data/resize_videos.py
mmaction/.mim/tools/data/activitynet/activitynet_feature_postprocessing.py
mmaction/.mim/tools/data/activitynet/convert_proposal_format.py
mmaction/.mim/tools/data/activitynet/download.py
mmaction/.mim/tools/data/activitynet/download_annotations.sh
mmaction/.mim/tools/data/activitynet/download_bsn_videos.sh
mmaction/.mim/tools/data/activitynet/download_feature_annotations.sh
mmaction/.mim/tools/data/activitynet/download_features.sh
mmaction/.mim/tools/data/activitynet/download_videos.sh
mmaction/.mim/tools/data/activitynet/extract_frames.sh
mmaction/.mim/tools/data/activitynet/generate_rawframes_filelist.py
mmaction/.mim/tools/data/activitynet/process_annotations.py
mmaction/.mim/tools/data/activitynet/tsn_extract_flow_feat_config.py
mmaction/.mim/tools/data/activitynet/tsn_extract_rgb_feat_config.py
mmaction/.mim/tools/data/activitynet/tsn_extract_video_feat_config.py
mmaction/.mim/tools/data/ava/cut_videos.sh
mmaction/.mim/tools/data/ava/download_annotations.sh
mmaction/.mim/tools/data/ava/download_videos.sh
mmaction/.mim/tools/data/ava/download_videos_gnu_parallel.sh
mmaction/.mim/tools/data/ava/download_videos_parallel.py
mmaction/.mim/tools/data/ava/download_videos_parallel.sh
mmaction/.mim/tools/data/ava/extract_frames.sh
mmaction/.mim/tools/data/ava/extract_rgb_frames.sh
mmaction/.mim/tools/data/ava/extract_rgb_frames_ffmpeg.sh
mmaction/.mim/tools/data/ava/fetch_ava_proposals.sh
mmaction/.mim/tools/data/ava_kinetics/X-101-64x4d-FPN.py
mmaction/.mim/tools/data/ava_kinetics/cut_kinetics.py
mmaction/.mim/tools/data/ava_kinetics/extract_rgb_frames.py
mmaction/.mim/tools/data/ava_kinetics/fetch_proposal.py
mmaction/.mim/tools/data/ava_kinetics/merge_annotations.py
mmaction/.mim/tools/data/ava_kinetics/prepare_annotation.py
mmaction/.mim/tools/data/ava_kinetics/softlink_ava.py
mmaction/.mim/tools/data/charades-sta/download_annotations.sh
mmaction/.mim/tools/data/diving48/download_annotations.sh
mmaction/.mim/tools/data/diving48/download_videos.sh
mmaction/.mim/tools/data/diving48/extract_frames.sh
mmaction/.mim/tools/data/diving48/extract_rgb_frames.sh
mmaction/.mim/tools/data/diving48/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/diving48/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/diving48/generate_videos_filelist.sh
mmaction/.mim/tools/data/diving48/preprocess.sh
mmaction/.mim/tools/data/gym/download.py
mmaction/.mim/tools/data/gym/download_annotations.sh
mmaction/.mim/tools/data/gym/download_videos.sh
mmaction/.mim/tools/data/gym/extract_frames.sh
mmaction/.mim/tools/data/gym/generate_file_list.py
mmaction/.mim/tools/data/gym/trim_event.py
mmaction/.mim/tools/data/gym/trim_subaction.py
mmaction/.mim/tools/data/hacs/generate_anotations.py
mmaction/.mim/tools/data/hacs/generate_list.py
mmaction/.mim/tools/data/hacs/slowonly_feature_infer.py
mmaction/.mim/tools/data/hacs/write_feature_csv.py
mmaction/.mim/tools/data/hmdb51/download_annotations.sh
mmaction/.mim/tools/data/hmdb51/download_videos.sh
mmaction/.mim/tools/data/hmdb51/extract_frames.sh
mmaction/.mim/tools/data/hmdb51/extract_rgb_frames.sh
mmaction/.mim/tools/data/hmdb51/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/hmdb51/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/hmdb51/generate_videos_filelist.sh
mmaction/.mim/tools/data/hvu/download.py
mmaction/.mim/tools/data/hvu/download_annotations.sh
mmaction/.mim/tools/data/hvu/download_videos.sh
mmaction/.mim/tools/data/hvu/extract_frames.sh
mmaction/.mim/tools/data/hvu/generate_file_list.py
mmaction/.mim/tools/data/hvu/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/hvu/generate_sub_file_list.py
mmaction/.mim/tools/data/hvu/generate_videos_filelist.sh
mmaction/.mim/tools/data/hvu/parse_tag_list.py
mmaction/.mim/tools/data/jester/encode_videos.sh
mmaction/.mim/tools/data/jester/extract_flow.sh
mmaction/.mim/tools/data/jester/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/jester/generate_videos_filelist.sh
mmaction/.mim/tools/data/kinetics/download.py
mmaction/.mim/tools/data/kinetics/download_annotations.sh
mmaction/.mim/tools/data/kinetics/download_backup_annotations.sh
mmaction/.mim/tools/data/kinetics/download_videos.sh
mmaction/.mim/tools/data/kinetics/extract_frames.sh
mmaction/.mim/tools/data/kinetics/extract_rgb_frames.sh
mmaction/.mim/tools/data/kinetics/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/kinetics/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/kinetics/generate_videos_filelist.sh
mmaction/.mim/tools/data/kinetics/preprocess_k400.sh
mmaction/.mim/tools/data/kinetics/preprocess_k600.sh
mmaction/.mim/tools/data/kinetics/preprocess_k700.sh
mmaction/.mim/tools/data/kinetics/rename_classnames.sh
mmaction/.mim/tools/data/mit/extract_frames.sh
mmaction/.mim/tools/data/mit/extract_rgb_frames.sh
mmaction/.mim/tools/data/mit/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/mit/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/mit/generate_videos_filelist.sh
mmaction/.mim/tools/data/mit/preprocess_data.sh
mmaction/.mim/tools/data/mmit/extract_frames.sh
mmaction/.mim/tools/data/mmit/extract_rgb_frames.sh
mmaction/.mim/tools/data/mmit/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/mmit/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/mmit/generate_videos_filelist.sh
mmaction/.mim/tools/data/mmit/preprocess_data.sh
mmaction/.mim/tools/data/msrvtt/compress.py
mmaction/.mim/tools/data/msrvtt/compress_msrvtt.sh
mmaction/.mim/tools/data/msrvtt/download_msrvtt.sh
mmaction/.mim/tools/data/multisports/format_det_result.py
mmaction/.mim/tools/data/multisports/parse_anno.py
mmaction/.mim/tools/data/omnisource/trim_raw_video.py
mmaction/.mim/tools/data/skeleton/babel2mma2.py
mmaction/.mim/tools/data/skeleton/compress_nturgbd.py
mmaction/.mim/tools/data/skeleton/gen_ntu_rgbd_raw.py
mmaction/.mim/tools/data/skeleton/ntu_pose_extraction.py
mmaction/.mim/tools/data/sthv1/encode_videos.sh
mmaction/.mim/tools/data/sthv1/extract_flow.sh
mmaction/.mim/tools/data/sthv1/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/sthv1/generate_videos_filelist.sh
mmaction/.mim/tools/data/sthv2/extract_frames.sh
mmaction/.mim/tools/data/sthv2/extract_rgb_frames.sh
mmaction/.mim/tools/data/sthv2/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/sthv2/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/sthv2/generate_videos_filelist.sh
mmaction/.mim/tools/data/sthv2/preprocss.sh
mmaction/.mim/tools/data/thumos14/denormalize_proposal_file.sh
mmaction/.mim/tools/data/thumos14/download_annotations.sh
mmaction/.mim/tools/data/thumos14/download_videos.sh
mmaction/.mim/tools/data/thumos14/extract_frames.sh
mmaction/.mim/tools/data/thumos14/extract_rgb_frames.sh
mmaction/.mim/tools/data/thumos14/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/thumos14/fetch_tag_proposals.sh
mmaction/.mim/tools/data/ucf101/download_annotations.sh
mmaction/.mim/tools/data/ucf101/download_videos.sh
mmaction/.mim/tools/data/ucf101/extract_frames.sh
mmaction/.mim/tools/data/ucf101/extract_rgb_frames.sh
mmaction/.mim/tools/data/ucf101/extract_rgb_frames_opencv.sh
mmaction/.mim/tools/data/ucf101/generate_rawframes_filelist.sh
mmaction/.mim/tools/data/ucf101/generate_videos_filelist.sh
mmaction/.mim/tools/data/video_retrieval/prepare_msrvtt.py
mmaction/.mim/tools/data/video_retrieval/prepare_msrvtt.sh
mmaction/.mim/tools/data/video_retrieval/prepare_msvd.py
mmaction/.mim/tools/data/video_retrieval/prepare_msvd.sh
mmaction/.mim/tools/deployment/export_onnx_gcn.py
mmaction/.mim/tools/deployment/export_onnx_posec3d.py
mmaction/.mim/tools/deployment/export_onnx_stdet.py
mmaction/.mim/tools/deployment/mmaction2torchserve.py
mmaction/.mim/tools/deployment/mmaction_handler.py
mmaction/.mim/tools/deployment/publish_model.py
mmaction/.mim/tools/misc/bsn_proposal_generation.py
mmaction/.mim/tools/misc/clip_feature_extraction.py
mmaction/.mim/tools/misc/dist_clip_feature_extraction.sh
mmaction/.mim/tools/misc/flow_extraction.py
mmaction/.mim/tools/moss/Pose_pipline.py
mmaction/.mim/tools/moss/Pose_pipline_Simple.py
mmaction/.mim/tools/moss/create_vidlabel_lst.py
mmaction/.mim/tools/moss/source_script.sh
mmaction/.mim/tools/moss/t1.sh
mmaction/.mim/tools/moss/visiable_preprocess.py
mmaction/.mim/tools/visualizations/browse_dataset.py
mmaction/.mim/tools/visualizations/vis_cam.py
mmaction/.mim/tools/visualizations/vis_scheduler.py
mmaction/.mim/tools/work_dirs/train_multimodal.sh
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250730_121806_padding/multimodal_poseGCN-rgbR50_fusion.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250730_121806_padding/vis_data/config.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250801_100506_R50_512/multimodal_poseGCN-rgbR50_fusion.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250801_100506_R50_512/vis_data/config.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250805_045639/multimodal_poseGCN-rgbR50_fusion.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250805_045639/vis_data/config.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250806_045654/vis_data/config.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250806_072035_atten/multimodal_poseGCN-rgbR50_fusion.py
mmaction/.mim/tools/work_dirs/pose_rgb_fusion/20250806_072035_atten/vis_data/config.py
mmaction/apis/__init__.py
mmaction/apis/inference.py
mmaction/apis/inferencers/__init__.py
mmaction/apis/inferencers/actionrecog_inferencer.py
mmaction/apis/inferencers/mmaction2_inferencer.py
mmaction/datasets/__init__.py
mmaction/datasets/activitynet_dataset.py
mmaction/datasets/audio_dataset.py
mmaction/datasets/ava_dataset.py
mmaction/datasets/base.py
mmaction/datasets/charades_sta_dataset.py
mmaction/datasets/msrvtt_datasets.py
mmaction/datasets/multimodal_dataset.py
mmaction/datasets/pose_dataset.py
mmaction/datasets/rawframe_dataset.py
mmaction/datasets/repeat_aug_dataset.py
mmaction/datasets/video_dataset.py
mmaction/datasets/video_text_dataset.py
mmaction/datasets/transforms/__init__.py
mmaction/datasets/transforms/formatting.py
mmaction/datasets/transforms/loading.py
mmaction/datasets/transforms/mixPoseRGB_transforms.py
mmaction/datasets/transforms/pose_transforms.py
mmaction/datasets/transforms/processing.py
mmaction/datasets/transforms/text_transforms.py
mmaction/datasets/transforms/wrappers.py
mmaction/engine/__init__.py
mmaction/engine/hooks/__init__.py
mmaction/engine/hooks/class_weight_hook.py
mmaction/engine/hooks/hard_sample_hook.py
mmaction/engine/hooks/output.py
mmaction/engine/hooks/visualization_hook.py
mmaction/engine/model/__init__.py
mmaction/engine/model/weight_init.py
mmaction/engine/optimizers/__init__.py
mmaction/engine/optimizers/layer_decay_optim_wrapper_constructor.py
mmaction/engine/optimizers/swin_optim_wrapper_constructor.py
mmaction/engine/optimizers/tsm_optim_wrapper_constructor.py
mmaction/engine/runner/__init__.py
mmaction/engine/runner/multi_loop.py
mmaction/engine/runner/retrieval_loop.py
mmaction/evaluation/__init__.py
mmaction/evaluation/functional/__init__.py
mmaction/evaluation/functional/accuracy.py
mmaction/evaluation/functional/ava_utils.py
mmaction/evaluation/functional/eval_detection.py
mmaction/evaluation/functional/multisports_utils.py
mmaction/evaluation/functional/ava_evaluation/__init__.py
mmaction/evaluation/functional/ava_evaluation/metrics.py
mmaction/evaluation/functional/ava_evaluation/np_box_list.py
mmaction/evaluation/functional/ava_evaluation/np_box_ops.py
mmaction/evaluation/metrics/__init__.py
mmaction/evaluation/metrics/acc_metric.py
mmaction/evaluation/metrics/anet_metric.py
mmaction/evaluation/metrics/ava_metric.py
mmaction/evaluation/metrics/multimodal_metric.py
mmaction/evaluation/metrics/multisports_metric.py
mmaction/evaluation/metrics/retrieval_metric.py
mmaction/evaluation/metrics/video_grounding_metric.py
mmaction/models/__init__.py
mmaction/models/backbones/TimeSformer.py
mmaction/models/backbones/__init__.py
mmaction/models/backbones/aagcn.py
mmaction/models/backbones/c2d.py
mmaction/models/backbones/c3d.py
mmaction/models/backbones/mobilenet_v2.py
mmaction/models/backbones/mobilenet_v2_tsm.py
mmaction/models/backbones/mobileone_tsm.py
mmaction/models/backbones/mvit.py
mmaction/models/backbones/resnet.py
mmaction/models/backbones/resnet2plus1d.py
mmaction/models/backbones/resnet3d.py
mmaction/models/backbones/resnet3d_csn.py
mmaction/models/backbones/resnet3d_slowfast.py
mmaction/models/backbones/resnet3d_slowonly.py
mmaction/models/backbones/resnet_audio.py
mmaction/models/backbones/resnet_omni.py
mmaction/models/backbones/resnet_tin.py
mmaction/models/backbones/resnet_tsm.py
mmaction/models/backbones/rgbposeconv3d.py
mmaction/models/backbones/stgcn.py
mmaction/models/backbones/swin.py
mmaction/models/backbones/tanet.py
mmaction/models/backbones/uniformer.py
mmaction/models/backbones/uniformerv2.py
mmaction/models/backbones/vit_mae.py
mmaction/models/backbones/x3d.py
mmaction/models/common/__init__.py
mmaction/models/common/conv2plus1d.py
mmaction/models/common/conv_audio.py
mmaction/models/common/sub_batchnorm3d.py
mmaction/models/common/tam.py
mmaction/models/common/transformer.py
mmaction/models/data_preprocessors/__init__.py
mmaction/models/data_preprocessors/data_preprocessor.py
mmaction/models/data_preprocessors/multimodal_data_preprocessor.py
mmaction/models/heads/__init__.py
mmaction/models/heads/base.py
mmaction/models/heads/feature_head.py
mmaction/models/heads/gcn_head.py
mmaction/models/heads/i3d_head.py
mmaction/models/heads/mvit_head.py
mmaction/models/heads/omni_head.py
mmaction/models/heads/rgbpose_head.py
mmaction/models/heads/slowfast_head.py
mmaction/models/heads/timesformer_head.py
mmaction/models/heads/tpn_head.py
mmaction/models/heads/trn_head.py
mmaction/models/heads/tsm_head.py
mmaction/models/heads/tsn_audio_head.py
mmaction/models/heads/tsn_head.py
mmaction/models/heads/twofusion_head.py
mmaction/models/heads/uniformer_head.py
mmaction/models/heads/x3d_head.py
mmaction/models/localizers/__init__.py
mmaction/models/localizers/bmn.py
mmaction/models/localizers/bsn.py
mmaction/models/localizers/tcanet.py
mmaction/models/localizers/utils/__init__.py
mmaction/models/localizers/utils/bsn_utils.py
mmaction/models/localizers/utils/proposal_utils.py
mmaction/models/localizers/utils/tcanet_utils.py
mmaction/models/losses/__init__.py
mmaction/models/losses/base.py
mmaction/models/losses/binary_logistic_regression_loss.py
mmaction/models/losses/bmn_loss.py
mmaction/models/losses/cross_entropy_loss.py
mmaction/models/losses/focal_loss.py
mmaction/models/losses/hvu_loss.py
mmaction/models/losses/nll_loss.py
mmaction/models/losses/ohem_hinge_loss.py
mmaction/models/losses/ssn_loss.py
mmaction/models/multimodal/__init__.py
mmaction/models/multimodal/vindlu/__init__.py
mmaction/models/multimodal/vindlu/beit3d.py
mmaction/models/multimodal/vindlu/modeling_bert.py
mmaction/models/multimodal/vindlu/temporal_model.py
mmaction/models/multimodal/vindlu/tokenizer.py
mmaction/models/multimodal/vindlu/utils.py
mmaction/models/multimodal/vindlu/vindlu.py
mmaction/models/multimodal/vindlu/vindlu_ret.py
mmaction/models/multimodal/vindlu/vindlu_ret_mc.py
mmaction/models/multimodal/vindlu/vindlu_vqa.py
mmaction/models/multimodal/vindlu/xbert.py
mmaction/models/necks/__init__.py
mmaction/models/necks/fusion_neck.py
mmaction/models/necks/tpn.py
mmaction/models/recognizers/__init__.py
mmaction/models/recognizers/base.py
mmaction/models/recognizers/multimodal_recognizer.py
mmaction/models/recognizers/multimodal_recognizer_R50TSM512.py
mmaction/models/recognizers/recognizer2d.py
mmaction/models/recognizers/recognizer3d.py
mmaction/models/recognizers/recognizer3d_mm.py
mmaction/models/recognizers/recognizer_audio.py
mmaction/models/recognizers/recognizer_gcn.py
mmaction/models/recognizers/recognizer_omni.py
mmaction/models/roi_heads/__init__.py
mmaction/models/roi_heads/roi_head.py
mmaction/models/roi_heads/bbox_heads/__init__.py
mmaction/models/roi_heads/bbox_heads/bbox_head.py
mmaction/models/roi_heads/roi_extractors/__init__.py
mmaction/models/roi_heads/roi_extractors/single_straight3d.py
mmaction/models/roi_heads/shared_heads/__init__.py
mmaction/models/roi_heads/shared_heads/acrn_head.py
mmaction/models/roi_heads/shared_heads/fbo_head.py
mmaction/models/roi_heads/shared_heads/lfb.py
mmaction/models/roi_heads/shared_heads/lfb_infer_head.py
mmaction/models/similarity/__init__.py
mmaction/models/similarity/adapters.py
mmaction/models/similarity/clip_similarity.py
mmaction/models/task_modules/__init__.py
mmaction/models/task_modules/assigners/__init__.py
mmaction/models/task_modules/assigners/max_iou_assigner_ava.py
mmaction/models/utils/__init__.py
mmaction/models/utils/blending_utils.py
mmaction/models/utils/embed.py
mmaction/models/utils/gcn_utils.py
mmaction/models/utils/graph.py
mmaction/structures/__init__.py
mmaction/structures/action_data_sample.py
mmaction/structures/bbox/__init__.py
mmaction/structures/bbox/bbox_target.py
mmaction/structures/bbox/transforms.py
mmaction/testing/__init__.py
mmaction/testing/_utils.py
mmaction/utils/__init__.py
mmaction/utils/class_weight_utils.py
mmaction/utils/collect_env.py
mmaction/utils/dependency.py
mmaction/utils/gradcam_utils.py
mmaction/utils/misc.py
mmaction/utils/progress.py
mmaction/utils/setup_env.py
mmaction/utils/typing_utils.py
mmaction/visualization/__init__.py
mmaction/visualization/action_visualizer.py
mmaction/visualization/video_backend.py
mmaction2.egg-info/PKG-INFO
mmaction2.egg-info/SOURCES.txt
mmaction2.egg-info/dependency_links.txt
mmaction2.egg-info/not-zip-safe
mmaction2.egg-info/requires.txt
mmaction2.egg-info/top_level.txt