decord>=0.4.1
einops
matplotlib
numpy
opencv-contrib-python
Pillow
scipy
torch>=1.3

[all]
decord>=0.4.1
einops
matplotlib
numpy
opencv-contrib-python
Pillow
scipy
torch>=1.3
av>=9.0
future
imgaug
librosa
lmdb
moviepy
openai-clip
packaging
pims
PyTurboJPEG
soundfile
tensorboard
wandb
coverage
flake8
interrogate
isort==4.3.21
parameterized
pytest
pytest-runner
xdoctest>=0.10.0
yapf

[mim]
mmcv<2.2.0,>=2.0.0rc4
mmengine<1.0.0,>=0.7.1

[multimodal]
transformers>=4.28.0

[optional]
av>=9.0
future
imgaug
librosa
lmdb
moviepy
openai-clip
packaging
pims
PyTurboJPEG
soundfile
tensorboard
wandb

[tests]
coverage
flake8
interrogate
isort==4.3.21
parameterized
pytest
pytest-runner
xdoctest>=0.10.0
yapf
