# 需求文档

## 介绍

该功能旨在使用正常仰卧起坐样本作为输入，生成合成的作弊行为数据。系统将增强现有的正常仰卧起坐数据（显示躺平、起身和促膝抱头位置的3帧序列），创建模拟外部辅助（使用绳子、衣服或其他工具）的真实作弊行为样本。这将有助于平衡数据集，用于训练3分类模型来检测仰卧起坐运动中的正常与作弊行为。

## 需求

### 需求 1

**用户故事：** 作为机器学习研究员，我希望从正常仰卧起坐样本生成合成作弊行为数据，以便为训练我的仰卧起坐行为分类模型创建平衡的数据集。

#### 验收标准

1. 当系统处理正常仰卧起坐样本（3张JPG图片）时，系统应生成相应的模拟外部辅助的作弊行为图片
2. 当生成作弊数据时，系统应保持相同的3帧结构（躺平、起身、促膝抱头位置）
3. 当处理输入数据时，系统默认从项目目录下 `pose_rgb/0_normal` 读取（可在网页“路径配置”处自定义）
4. 当生成输出时，系统默认保存在项目目录下 `pose_rgb/Fake_AI`（可在网页“路径配置”处自定义）

### 需求 2

**用户故事：** 作为数据科学家，我希望生成的作弊样本能够真实地模拟外部工具辅助，以便模型能够学习检测实际的作弊行为。

#### 验收标准

1. 当生成作弊行为时，系统应模拟外部辅助的视觉特征（绳子、衣服或其他工具）
2. 当创建合成样本时，系统应确保作弊行为在3帧序列中看起来真实且一致
3. 当处理样本时，系统应在添加作弊元素的同时保持姿势一致性
4. 在项目目录 `pose_rgb/Fake_tool` 中存在参考作弊样本（或由用户指定路径），系统应将其用作生成真实作弊行为的风格参考

### 需求 3

**用户故事：** 作为开发者，我希望系统能够高效地处理多个样本的批量处理，以便快速生成大量训练数据。

#### 验收标准

1. 当处理数据集时，系统应自动发现正常数据集目录中包含3张JPG图片的所有PKL文件夹
2. 当批量处理时，系统应在可能的情况下并行处理多个样本
3. 当遇到错误时，系统应记录问题并继续处理剩余样本
4. 当处理完成时，系统应提供生成样本的摘要报告

### 需求 4

**用户故事：** 作为机器学习工程师，我希望生成的数据保持适当的元数据和组织结构，以便我可以轻松地将其集成到我的训练流水线中。

#### 验收标准

1. 当保存生成的样本时，系统应保持与原始数据集相同的文件命名约定
2. 当创建输出目录时，系统应在清晰的文件夹结构中组织生成的样本
3. 当生成样本时，系统应保持与原始数据一致的图像质量
4. 当处理完成时，系统应生成描述合成样本和生成参数的元数据文件


注意：系统已移除任何对本地绝对路径的依赖。默认路径均基于项目目录计算，并可在网页“路径配置”中进行更改。如需迁移到新机器，无需创建 `/media/pyl`。

