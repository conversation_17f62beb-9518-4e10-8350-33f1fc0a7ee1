# -*-coding:utf-8-*-
import torch
import torch.nn as nn
from mmaction.registry import MODELS
from mmaction.models.heads.base import BaseHead


@MODELS.register_module(force=True)
class TwoFusionHead_V0(BaseHead):
    """
    多模态融合特征（图像+骨骼）分类头   created by @Moss: 20250604 17:05
    *Ref claude4 Team
    """
    def __init__(self,
                 num_classes=3,
                 in_channels=512,  # 融合特征维度
                 dropout_ratio=0.5,
                 init_std=0.01,
                 **kwargs):
        super().__init__(num_classes, in_channels, **kwargs)

        self.dropout_ratio = dropout_ratio
        self.init_std = init_std

        if self.dropout_ratio != 0:
            self.dropout = nn.Dropout(p=self.dropout_ratio)
        else:
            self.dropout = None

        # 多层MLP用于特征细化
        self.fc_layers = nn.Sequential(
            nn.Linear(in_channels, in_channels // 2),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(in_channels // 2),
            nn.Dropout(p=dropout_ratio) if dropout_ratio > 0 else nn.Identity(),

            nn.Linear(in_channels // 2, in_channels // 4),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(in_channels // 4),
            nn.Dropout(p=dropout_ratio) if dropout_ratio > 0 else nn.Identity()
        )

        # 最终分类层
        self.fc_cls = nn.Linear(in_channels // 4, num_classes)

    def init_weights(self):
        """Initialize the weights."""
        for m in self.fc_layers:
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, self.init_std)
                nn.init.constant_(m.bias, 0)
        nn.init.normal_(self.fc_cls.weight, 0, self.init_std)
        nn.init.constant_(self.fc_cls.bias, 0)

    def forward(self, x, **kwargs):
        """Forward function.

        Args:
            x (torch.Tensor): The input tensor of shape (N, in_channels).

        Returns:
            torch.Tensor: The classification scores of shape (N, num_classes).
        """
        # 确保输入是2D的
        if x.dim() > 2:
            x = x.view(x.size(0), -1)

        # 通过MLP层
        x = self.fc_layers(x)

        # 最终分类
        cls_score = self.fc_cls(x)

        return cls_score




@MODELS.register_module(force=True)
class TwoFusionHead(BaseHead):
    """
    优化版多模态融合特征分类头
    主要改进：
    1. 支持输出概率化（softmax/sigmoid）
    2. 提供直接映射选项（512→3，避免欠拟合）
    3. 更好的权重初始化
    4. 灵活的激活函数选择
    5. 大幅减少参数量（99.1%减少）

    推荐使用 network_type='direct' 以获得最佳性能
    Created by: Optimization based on original TwoFusionHead
    """
    def __init__(self,
                 num_classes: int = 3,
                 in_channels: int = 512,
                 dropout_ratio: float = 0.5,
                 init_std: float = 0.01,
                 activation: str = 'softmax',  # 'softmax', 'sigmoid', 'none'
                 network_type: str = 'direct',  # 'direct', 'simplified', 'original'
                 hidden_ratio: float = 0.5,  # 隐藏层维度比例（仅simplified模式使用）
                 **kwargs):
        super().__init__(num_classes, in_channels, **kwargs)

        self.dropout_ratio = dropout_ratio
        self.init_std = init_std
        self.activation = activation
        self.network_type = network_type
        self.hidden_ratio = hidden_ratio

        # 构建网络结构
        if network_type == 'direct':
            self._build_direct_network()
        elif network_type == 'simplified':
            self._build_simplified_network()
        else:  # 'original'
            self._build_original_network()

        # 激活函数
        if activation == 'softmax':
            self.activate_fn = nn.Softmax(dim=1)
        elif activation == 'sigmoid':
            self.activate_fn = nn.Sigmoid()
        else:  # 'none'
            self.activate_fn = nn.Identity()

    def _build_direct_network(self):
        """构建直接映射网络：512 → 3，避免欠拟合"""
        # 只使用dropout和直接分类层
        if self.dropout_ratio > 0:
            self.dropout = nn.Dropout(p=self.dropout_ratio)
        else:
            self.dropout = nn.Identity()

        # 直接分类层：in_channels → num_classes
        self.fc_cls = nn.Linear(self.in_channels, self.num_classes)

        # 不需要fc_layers，设为Identity
        self.fc_layers = nn.Identity()

    def _build_simplified_network(self):
        """构建简化的网络结构，减少过拟合风险"""
        hidden_dim = int(self.in_channels * self.hidden_ratio)

        self.fc_layers = nn.Sequential(
            nn.Linear(self.in_channels, hidden_dim),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(p=self.dropout_ratio) if self.dropout_ratio > 0 else nn.Identity()
        )

        self.fc_cls = nn.Linear(hidden_dim, self.num_classes)

    def _build_original_network(self):
        """构建原始的复杂网络结构"""
        self.fc_layers = nn.Sequential(
            nn.Linear(self.in_channels, self.in_channels // 2),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(self.in_channels // 2),
            nn.Dropout(p=self.dropout_ratio) if self.dropout_ratio > 0 else nn.Identity(),

            nn.Linear(self.in_channels // 2, self.in_channels // 4),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(self.in_channels // 4),
            nn.Dropout(p=self.dropout_ratio) if self.dropout_ratio > 0 else nn.Identity()
        )

        self.fc_cls = nn.Linear(self.in_channels // 4, self.num_classes)

    def init_weights(self):
        """优化的权重初始化"""
        for m in self.fc_layers:
            if isinstance(m, nn.Linear):
                # 使用Xavier初始化，更适合ReLU激活函数
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)

        # 分类层使用更小的初始化标准差
        nn.init.normal_(self.fc_cls.weight, 0, self.init_std * 0.5)
        nn.init.constant_(self.fc_cls.bias, 0)

    def forward(self, x: torch.Tensor, return_logits: bool = False, **kwargs) -> torch.Tensor:
        """
        前向传播

        Args:
            x (torch.Tensor): 输入特征张量 (N, in_channels)
            return_logits (bool): 是否返回原始logits，默认False返回概率

        Returns:
            torch.Tensor: 分类分数或概率 (N, num_classes)
        """
        # 确保输入是2D的
        if x.dim() > 2:
            x = x.view(x.size(0), -1)

        # 根据网络类型处理
        if self.network_type == 'direct':
            # 直接映射：dropout → 分类层
            x = self.dropout(x)
            logits = self.fc_cls(x)
        else:
            # 其他类型：特征提取层 → 分类层
            x = self.fc_layers(x)
            logits = self.fc_cls(x)

        # 根据需要返回logits或概率
        if return_logits or self.activation == 'none':
            return logits
        else:
            return self.activate_fn(logits)


if __name__ == '__main__':
    # 设置随机种子以确保可重复性
    torch.manual_seed(2025)
    batch_size = 2

    # 测试原始版本
    print("=== 原始TwoFusionHead_V0测试 ===")
    cls_head_original = TwoFusionHead_V0(
        num_classes=3,
        in_channels=512,
        loss_cls=dict(
            type='CrossEntropyLoss',
            loss_weight=1.0,
            class_weight=[1.0, 2.0, 1.5]
        )
    )

    fusionFeature = torch.randn(batch_size, 512)
    output_original = cls_head_original.forward(fusionFeature)
    print(f"原始输出 (logits): {output_original}")
    print(f"输出形状: {output_original.shape}")

    # 测试优化版本 - 直接映射（推荐，避免欠拟合）
    print("\n=== 优化TwoFusionHead测试（直接映射）===")
    cls_head_direct = TwoFusionHead(
        num_classes=3,
        in_channels=512,
        activation='softmax',  # 输出概率
        network_type='direct',  # 直接映射512→3，避免欠拟合
        loss_cls=dict(
            type='CrossEntropyLoss',
            loss_weight=1.0,
            class_weight=[1.0, 2.0, 1.5]
        )
    )

    # 测试概率输出
    output_prob_direct = cls_head_direct.forward(fusionFeature)
    print(f"直接映射概率输出: {output_prob_direct}")
    print(f"概率和: {output_prob_direct.sum(dim=1)}")

    # 测试logits输出
    output_logits_direct = cls_head_direct.forward(fusionFeature, return_logits=True)
    print(f"直接映射Logits输出: {output_logits_direct}")

    # 参数量对比
    direct_params = sum(p.numel() for p in cls_head_direct.parameters())
    original_params = sum(p.numel() for p in cls_head_original.parameters())
    print(f"\n📊 参数量对比:")
    print(f"   原始版本参数量: {original_params:,}")
    print(f"   直接映射参数量: {direct_params:,}")
    print(f"   参数减少: {original_params - direct_params:,} ({(1-direct_params/original_params)*100:.1f}%)")

    print(f"输出形状: {output_prob_direct.shape}")
