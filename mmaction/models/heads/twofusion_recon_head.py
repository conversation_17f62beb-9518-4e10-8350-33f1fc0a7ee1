# -*-coding:utf-8-*-
import torch
import torch.nn as nn
import torch.nn.functional as F

from mmaction.registry import MODELS
from mmaction.models.heads.base import BaseHead


@MODELS.register_module(force=True)
class TwoFusionReconHead(BaseHead):
    """
    多模态融合后的特征分类头（带重构分支）
    - 输入: 融合特征 (B, in_channels)
    - 输出: 分类分数 (B, num_classes)
    - 训练: 同时优化分类损失 + 重构损失（MSE）

    设计动机：
    将前三类视作“正常”数据，鼓励其在潜在空间可被良好重构；
    对第4类（稀有/异常）样本，不施加或极小化重构约束，从而在特征空间形成分离。
    """

    def __init__(self,
                 num_classes: int = 4,
                 in_channels: int = 512,
                 latent_dim: int = 128,
                 hidden_dim: int = 256,
                 dropout_ratio: float = 0.5,
                 recon_loss_weight: float = 0.3,
                 anomaly_label_index: int = 3,
                 anomaly_recon_weight: float = 0.0,
                 init_std: float = 0.01,
                 **kwargs):
        super().__init__(num_classes, in_channels, **kwargs)

        self.latent_dim = latent_dim
        self.hidden_dim = hidden_dim
        self.dropout_ratio = dropout_ratio
        self.recon_loss_weight = recon_loss_weight
        self.anomaly_label_index = anomaly_label_index
        self.anomaly_recon_weight = anomaly_recon_weight
        self.init_std = init_std

        # 编码器: in_channels -> hidden_dim -> latent_dim
        self.encoder = nn.Sequential(
            nn.Linear(in_channels, hidden_dim),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(p=dropout_ratio) if dropout_ratio > 0 else nn.Identity(),
            nn.Linear(hidden_dim, latent_dim),
            nn.ReLU(inplace=True)
        )

        # 分类器: latent_dim -> num_classes
        self.fc_cls = nn.Linear(latent_dim, num_classes)

        # 解码器: latent_dim -> hidden_dim -> in_channels
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(hidden_dim),
            nn.Linear(hidden_dim, in_channels)
        )

    def init_weights(self):
        for m in list(self.encoder.modules()) + list(self.decoder.modules()):
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, self.init_std)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        nn.init.normal_(self.fc_cls.weight, 0, self.init_std)
        nn.init.constant_(self.fc_cls.bias, 0)

    def forward(self, x: torch.Tensor, return_latent: bool = False, return_recon: bool = False):
        # 保证输入为 (B, C)
        if x.dim() > 2:
            x = x.view(x.size(0), -1)

        z = self.encoder(x)
        cls_score = self.fc_cls(z)

        if return_latent or return_recon:
            recon = self.decoder(z)
            outputs = {'cls_score': cls_score}
            if return_latent:
                outputs['latent'] = z
            if return_recon:
                outputs['recon'] = recon
            return outputs
        return cls_score

    def loss(self, feats: torch.Tensor, data_samples, **kwargs):
        """
        计算分类损失 + 重构损失。
        - 对正常类（非 anomaly_label_index）样本施加重构约束
        - 对异常类（anomaly_label_index）样本降低或取消重构约束
        """
        device = feats.device

        # 前向得到分类分数与重构结果
        outs = self.forward(feats, return_recon=True)
        cls_scores: torch.Tensor = outs['cls_score']
        recon: torch.Tensor = outs['recon']

        # 分类损失与精度指标
        cls_losses = super().loss_by_feat(cls_scores, data_samples)

        # 提取标签以做重构损失掩码
        labels = [x.gt_label for x in data_samples]
        labels = torch.stack(labels).to(device)
        labels = labels.squeeze()
        if labels.shape == torch.Size([]):
            labels = labels.unsqueeze(0)

        # 每样本 MSE
        feats_flat = feats.view(feats.size(0), -1)
        recon_mse_per_sample = F.mse_loss(recon, feats_flat, reduction='none').mean(dim=1)

        # 正常样本权重=1，异常样本权重=anomaly_recon_weight（默认0）
        is_anomaly = (labels == self.anomaly_label_index).float()
        sample_weights = (1.0 - is_anomaly) + is_anomaly * float(self.anomaly_recon_weight)

        # 避免除以0
        denom = sample_weights.sum().clamp_min(1.0)
        loss_recon = (recon_mse_per_sample * sample_weights).sum() / denom

        losses = dict(**cls_losses)
        losses['loss_recon'] = loss_recon * float(self.recon_loss_weight)
        # 记录重构误差均值，便于监控
        losses['recon_err'] = recon_mse_per_sample.mean().detach()
        return losses


