# -*-coding:utf-8-*-
"""
created by @moss 20250604 15:33
"""
import torch
import torch.nn as nn
from mmaction.registry import MODELS



@MODELS.register_module()
class MultiModalFusionNeck(nn.Module):
    """
    *Ref Gemini and Claude Team
    2D姿态序列和多帧RGB图像特征融合模块"""

    def __init__(self,
                 pose_feat_dim=512,
                 img_feat_dim=512,
                 fusion_dim=512,
                 num_heads=4,
                 fusion_type='attention',
                 dropout=0.5):
        super().__init__()

        self.fusion_type = fusion_type
        self.pose_feat_dim = pose_feat_dim
        self.img_feat_dim = img_feat_dim
        self.fusion_dim = fusion_dim            # Output dim of the neck
        self.num_heads = num_heads

        # 特征投影层
        self.pose_proj = nn.Linear(pose_feat_dim, fusion_dim)
        self.img_proj = nn.Linear(img_feat_dim, fusion_dim)

        if fusion_type == 'attention':
            self._build_attention_fusion()
        elif fusion_type == 'temporal_attention':
            self._build_temporal_attention_fusion()
        elif fusion_type == 'cross_modal':
            self._build_cross_modal_fusion()

        self.dropout = nn.Dropout(dropout)

    def _build_attention_fusion(self):
        """标准注意力融合"""
        self.multihead_attn = nn.MultiheadAttention(
            embed_dim=self.fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True  # 假设 (B, Seq, Feat) ,Seq=2
        )
        self.norm = nn.LayerNorm(self.fusion_dim)

    def _build_temporal_attention_fusion(self):
        """时序感知的注意力融合 - 专门针对姿态序列"""
        # 时序编码器
        self.temporal_encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=self.fusion_dim,
                nhead=8,
                dim_feedforward=2048,   # Often 4*d_model
                dropout=0.1,
                batch_first=True  # Assuming (B, Seq, Feat)
            ),
            num_layers=2
        )

        # 跨模态注意力
        self.cross_attn = nn.MultiheadAttention(
            embed_dim=self.fusion_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )

    def _build_cross_modal_fusion(self):
        """跨模态交互融合"""
        # 姿态->图像注意力
        self.pose_to_img_attn = nn.MultiheadAttention(
            embed_dim=self.fusion_dim, num_heads=4, batch_first=True
        )       # batch_first=True,则 shape (Batch, Seq, Feat)， 否则是 (Seq, Batch, Feat)
        # 图像->姿态注意力
        self.img_to_pose_attn = nn.MultiheadAttention(
            embed_dim=self.fusion_dim, num_heads=self.num_heads, batch_first=True
        )

        # 融合网络 结合双流特征
        self.fusion_net = nn.Sequential(
            nn.Linear(self.fusion_dim * 2, self.fusion_dim),
            nn.ReLU(),
            nn.Linear(self.fusion_dim, self.fusion_dim)         # Output is fusion_dim
        )

    def attention_fusion(self, pose_feat, img_feat):
        """标准注意力融合 - 修复batch_first参数冲突"""
        # 投影到统一维度
        pose_proj = self.pose_proj(pose_feat)  # (B, fusion_dim)
        img_proj = self.img_proj(img_feat)  # (B, fusion_dim)

        # 堆叠为序列，保持batch_first=True的格式
        features = torch.stack([pose_proj, img_proj], dim=1)  # (B, 2, fusion_dim)
        # 移除错误的transpose操作，直接使用batch_first格式

        # 多头注意力 - 输入格式为(B, Seq, Feat)，符合batch_first=True
        attended, _ = self.multihead_attn(features, features, features)
        attended = self.norm(attended + features)

        # 平均池化得到最终特征 - 在序列维度上平均
        fused = attended.mean(dim=1)  # (B, fusion_dim) - 注意这里改为dim=1
        return fused

    def temporal_attention_fusion(self, pose_feat, img_feat):
        """时序感知注意力融合"""
        # 投影特征
        pose_proj = self.pose_proj(pose_feat)
        img_proj = self.img_proj(img_feat)

        # 为姿态特征添加时序信息（如果原始有时序维度的话）
        # 这里假设pose_feat已经是汇总的特征，如需要可以扩展

        # 时序编码（模拟时序信息）
        pose_with_time = pose_proj.unsqueeze(1)  # (B, 1, fusion_dim)
        img_with_time = img_proj.unsqueeze(1)  # (B, 1, fusion_dim)

        combined = torch.cat([pose_with_time, img_with_time], dim=1)  # (B, 2, fusion_dim)
        combined = combined.transpose(0, 1)  # (2, B, fusion_dim)

        # 时序编码
        temporal_encoded = self.temporal_encoder(combined)  # (2, B, fusion_dim)

        # 跨模态注意力
        cross_attended, _ = self.cross_attn(
            temporal_encoded, temporal_encoded, temporal_encoded
        )

        # 融合
        fused = cross_attended.mean(dim=0)  # (B, fusion_dim)
        return fused

    def cross_modal_fusion(self, pose_feat, img_feat):
        """跨模态交互融合"""
        # 投影
        pose_seq = self.pose_proj(pose_feat).unsqueeze(1)  # (B, 1, fusion_dim) Unsqueeze 1-dim for attention input if batch_first=True
        img_seq = self.img_proj(img_feat).unsqueeze(1)  # (B, 1, fusion_dim)

        # 跨模态注意力, select Batch First
        pose_attended, _ = self.pose_to_img_attn(
            query=pose_seq, key=img_seq, value=img_seq
        )
        img_attended, _ = self.img_to_pose_attn(
            query=img_seq, key=pose_seq, value=pose_seq
        )

        # 合并特征:
        pose_final = pose_attended.squeeze(1)  # (B, fusion_dim)
        img_final = img_attended.squeeze(1)  # (B, fusion_dim)

        # 融合网络
        combined = torch.cat([pose_final, img_final], dim=1)  # (B, fusion_dim*2)
        fused = self.fusion_net(combined)  # (B, fusion_dim)

        return fused

    def forward(self, pose_feat, img_feat):
        """前向传播"""
        if self.fusion_type == 'concat':
            pose_proj = self.pose_proj(pose_feat)
            img_proj = self.img_proj(img_feat)
            return torch.cat([pose_proj, img_proj], dim=1)
        elif self.fusion_type == 'attention':
            return self.dropout(self.attention_fusion(pose_feat, img_feat))
        elif self.fusion_type == 'temporal_attention':
            return self.dropout(self.temporal_attention_fusion(pose_feat, img_feat))
        elif self.fusion_type == 'cross_modal':
            return self.dropout(self.cross_modal_fusion(pose_feat, img_feat))
        else:
            raise ValueError(f"Unsupported fusion type: {self.fusion_type}")


if __name__ == '__main__':
    torch.manual_seed(2025)
    # 定义输入特征维度
    batch_size = 2
    pose_feat_dim = 256  # ST-GCN 输出维度
    img_feat_dim = 2048  # ResNet50 输出维度
    fusion_dim = 512

    # 创建随机输入张量
    pose_feat = torch.randn(batch_size, pose_feat_dim)  # 随机姿态特征
    img_feat = torch.randn(batch_size, img_feat_dim)  # 随机图像特征

    # 创建融合模块实例
    fusion_neck = MultiModalFusionNeck(
        pose_feat_dim=pose_feat_dim,
        img_feat_dim=img_feat_dim,
        fusion_dim=fusion_dim,
        fusion_type='cross_modal',  # 跨模态融合
        dropout=0.5
    )

    # 前向传播
    fused_feat = fusion_neck(pose_feat, img_feat)  # 调用 forward 方法

    # 输出结果
    print("Fused output shape:", fused_feat.shape)      # (batch, fusion_dim)