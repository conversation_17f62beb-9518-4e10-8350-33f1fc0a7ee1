# 创建独立推理脚本以简化测试流程

## 背景
当前项目使用MMAction2框架，训练和测试流程都需要经过以下步骤：
1. 生成标签文件, 参考：@create_vidlabel_adaptive.py
2. 基于标签生成pkl格式数据， 参考：@custom_PoseRGB_extraction.py
3. 使用tools/train_m2.py进行模型训练推理，使用tools/test.py 进行模型测试推理

## 目标
创建一个独立的推理脚本，简化测试数据的处理流程，避免手动生成标签和pkl文件的步骤。

## 具体需求

### 核心功能
1. **直接数据读取**：给定数据路径后，脚本能够直接读取原始样本数据进行推理，跳过标签和pkl生成步骤
2. **批处理和GPU配置**：支持设置batch size和GPU数量，实现边读取边推理的流水线处理
3. **多卡支持**：同时支持单卡和多卡推理，充分利用CPU和GPU资源

### 配置和模型加载
4. **配置文件复用**：继续使用现有配置文件加载模型和预处理管道，不修改配置文件内容
5. **直接路径调用**：基于配置文件路径直接调用模型和预处理组件，绕过MMAction2的注册机制以提高效率

### 实现约束
6. **非侵入式实现**：通过新增文件的方式实现功能，不影响现有的训练测试流程和配置文件
7. **环境要求**：脚本需要在conda的torch环境下运行和测试

## 参考文件路径
重要参考流程： /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808/tools/detect_samples.sh
- 配置文件：`configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py`
- 训练脚本：`tools/train_m2.py`  
- 测试脚本：`tools/test.py`

## 预期输出
创建一个新的推理脚本，能够：
- 接受数据路径作为输入参数， 测试数据参考：/media/pyl/WD_Blue_1T/All_proj/classify_cheat
- 支持batch size和GPU数量配置
- 实现高效的数据加载和推理流水线
- 输出推理结果与原默认的测试样本的输出保持一致

请分析现有代码结构，设计并实现这个独立推理脚本。