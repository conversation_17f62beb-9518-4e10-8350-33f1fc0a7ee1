#!/bin/bash
# 方案A：渐进式学习率优化实验启动脚本
# 创建时间：2025-08-12

echo "=========================================="
echo "方案A：渐进式学习率优化实验"
echo "=========================================="
echo "实验特点："
echo "- 3阶段预热：0.0001 → 0.001 → 0.01"
echo "- 分层学习率：cls_head = 1.2x"
echo "- 周期性重启：第50 epoch"
echo "- 基于实验14最佳配置"
echo "=========================================="

# 检查环境
echo "检查环境..."
if ! command -v nvidia-smi &> /dev/null; then
    echo "错误：未检测到NVIDIA GPU"
    exit 1
fi

# 显示GPU状态
echo "GPU状态："
nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits

# 检查配置文件
CONFIG_FILE="configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_expA_progressive_lr.py"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误：配置文件不存在 $CONFIG_FILE"
    exit 1
fi

echo "配置文件检查通过：$CONFIG_FILE"

# 创建工作目录
WORK_DIR="work_dirs/pose_rgb_fusion"
mkdir -p "$WORK_DIR"

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
EXP_NAME="expA_progressive_lr_$TIMESTAMP"

echo "实验名称：$EXP_NAME"
echo "工作目录：$WORK_DIR/$EXP_NAME"

# 启动训练
echo "=========================================="
echo "开始训练..."
echo "=========================================="

# 使用4卡训练
python -m torch.distributed.launch \
    --nproc_per_node=4 \
    --master_port=29500 \
    tools/train_expA_progressive_lr.py \
    "$CONFIG_FILE" \
    --work-dir "$WORK_DIR/$EXP_NAME" \
    --launcher pytorch

# 检查训练结果
if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "训练完成！"
    echo "结果保存在：$WORK_DIR/$EXP_NAME"
    echo "=========================================="
    
    # 显示关键结果
    if [ -f "$WORK_DIR/$EXP_NAME/train.log" ]; then
        echo "最终验证精度："
        tail -20 "$WORK_DIR/$EXP_NAME/train.log" | grep "acc/top1"
    fi
else
    echo "=========================================="
    echo "训练失败！请检查日志文件"
    echo "日志位置：$WORK_DIR/$EXP_NAME"
    echo "=========================================="
    exit 1
fi
