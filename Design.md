
# [模式：规划]

好的，我们采纳“为每一帧图像都进行手动标注”的方案。这确保了最高的数据质量。接下来，我将为您制定一份详尽的技术实施计划。

**[更改计划]**

*   **文件**：创建一个新的 Python 脚本 `interactive_generator.py`。
*   **理由**：此脚本将作为交互式数据增强工具的核心，它将引导用户完成标注、处理图像叠加，并自动化文件管理流程，完全依据我们之前敲定的“交互式关键点标注 + 图像叠加”方案。

**必需的规划元素：**

1.  **依赖项**:
    *   `opencv-python`: 用于图像显示、处理和用户交互 (鼠标点击)。
    *   `numpy`: 用于高效的数组计算，尤其是在图像变换时。

2.  **核心组件与函数签名**:

    *   `select_keypoints(image: np.ndarray) -> list[tuple[int, int]]`:
        *   **功能**: 在一个窗口中显示输入的 `image`。
        *   **交互**: 使用 `cv2.setMouseCallback` 捕捉用户两次鼠标左键点击的坐标。
        *   **输出**: 返回一个包含两个 `(x, y)` 坐标元组的列表。

    *   `overlay_tool_on_image(background_img: np.ndarray, tool_img: np.ndarray, keypoints: list[tuple[int, int]]) -> np.ndarray`:
        *   **功能**: 将 `tool_img`（作弊工具的透明PNG图片）叠加到 `background_img` 上。
        *   **逻辑**:
            1.  根据 `keypoints`（用户标注的两个手腕坐标）计算它们之间的距离和角度。
            2.  对 `tool_img` 进行缩放和旋转，使其两个端点能够精准地对齐这两个 `keypoints`。
            3.  利用 `tool_img` 的Alpha通道（透明通道）作为蒙版，将其无缝地合成到 `background_img` 上。
        *   **输出**: 返回合成后的新图像。

    *   `process_all_samples(input_dir: str, output_dir: str, tool_dir: str)`:
        *   **功能**: 主流程函数，负责整个数据增强任务的编排。
        *   **逻辑**:
            1.  检查并创建 `output_dir`。
            2.  扫描 `tool_dir`，加载所有可用的作弊工具图片（背景透明的PNG）。
            3.  遍历 `input_dir` 下的所有样本子目录。
            4.  对于每个样本：
                a.  在 `output_dir` 中创建对应的子目录。
                b.  加载样本内的3张JPG图片。
                c.  随机为该样本挑选一个作弊工具。
                d.  **按顺序对3张图片中的每一张**，调用 `select_keypoints` 让用户标注，然后调用 `overlay_tool_on_image` 生成作弊图片。
                e.  将生成的三张图片保存到输出子目录。
                f.  将原始样本中的 `.avi` 和 `.pkl` 文件拷贝到输出子目录。
                g.  在控制台打印进度。
            5.  任务结束后，打印总结报告。

3.  **前提条件**:
    *   用户需要预先将项目目录 `pose_rgb/Fake_tool`（或在网页“路径配置”中自定义的工具路径）下的作弊工具参考图处理成**背景透明的PNG文件**，以便脚本进行无缝叠加。

**实施清单：**

1.  创建新文件 `interactive_generator.py`。
2.  在脚本顶部导入所需库: `cv2`, `numpy`, `os`, `shutil`, `glob`, `random`。
3.  定义输入、输出及工具目录的常量路径。
4.  实现 `select_keypoints` 函数，用于显示图像并捕捉用户的鼠标点击。
5.  实现 `overlay_tool_on_image` 函数，完成核心的图像旋转、缩放和叠加逻辑。
6.  实现 `process_all_samples` 主函数，编排整个处理流程，包括文件遍历、用户交互循环、文件保存和元数据拷贝。
7.  添加 `if __name__ == "__main__":` 入口，调用主函数 `process_all_samples`。
8.  创建一个 `requirements.txt` 文件，并写入依赖项 `opencv-python` 和 `numpy`。

---

## 方案演进：两阶段交互式工具

**背景**: 初步测试发现，`Fake_tool` 目录中缺少可直接使用的、背景透明的作弊工具“蒙版”素材，仅有合成后的最终效果图。这使得原计划无法执行。

**新构思**: 将脚本升级为一个多功能工具，包含两种核心操作模式，形成一个从素材创建到数据生成的完整闭环。

### 模式一：创建蒙版 (Mask Creation)

此模式专门用于从用户提供的效果示例图中，通过交互式操作，提取出作弊工具，并将其制作成背景透明的PNG蒙版。

**流程**:
1.  **选择源图**: 脚本列出 `Fake_tool` 目录中的所有图片，用户选择一张作为处理对象。
2.  **交互式勾勒**: 在窗口中显示该图片。用户通过鼠标左键点击，围绕作弊工具（如绳子、衣服）的边缘绘制一个精确的多边形。
3.  **提取与保存**:
    *   用户确认后，脚本根据绘制的多边形生成一个蒙版。
    *   利用此蒙版，将工具从原始背景中“抠”出。
    *   将抠出的、背景透明的工具图像保存为一个新的PNG文件到新目录 `Fake_tool_masks` 中。

### 模式二：生成数据 (Data Generation)

此模式基本沿用我们最初的计划，但其素材来源发生了改变。

**流程**:
1.  **加载蒙版**: 脚本从 `Fake_tool_masks` 目录加载所有可用的、背景透明的工具蒙版。
2.  **交互式标注**: 遍历 `0_normal` 目录下的正常样本，对于每一帧，用户通过鼠标点击标注两个手腕的位置。
3.  **合成与保存**: 脚本将随机选择的工具蒙版，根据用户标注的位置进行缩放、旋转并叠加到正常样本上，生成作弊样本，并保存到 `Fake_AI` 目录，同时拷贝元数据。

**优势**:
- **自给自足**: 工具本身具备了创建所需素材的能力，形成闭环。
- **高精度**: 用户手动勾勒轮廓，确保了蒙版质量。
- **流程清晰**: 先通过模式一准备素材，再通过模式二批量生成数据。

