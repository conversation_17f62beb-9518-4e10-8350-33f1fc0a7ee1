默认配置： /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808/configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py

原方案背景:
仰卧起坐场景理解与作弊违规检测：
通过3分类模型，识别单次仰卧其坐过程中的 正常、作弊行为 与 过程中未抱头行为(包含单手抱头)
(1) 主要的作弊行为: 借助外力作弊(将1只手藏在摄像头盲区, 拉拽衣脚起身; 借助绳子、衣服等工具拉拽人体完成仰卧起坐的作弊行为; 推拉扶他人配合动作作弊；
(2) 主要的未抱头行为: 仰卧起坐从起身开始到促膝，过程中存在任1只手未抱头的行为
(3) 正常单次仰卧起坐: 从躺平到起身到促膝，全程双手抱头
第4个类别暂未缺少足够的数据，主要包括未促膝，腿未弯曲等不标准动作，每个人不标准的姿态有差异；
3. 模型描述：
模型采用双模态融合特征分类模型, 分为4个部分：
(1) 骨骼模块: 采用ST-GCN的特征层，提取骨骼的时序特征(主要想要获取正常动作和过程中未抱头的运动特征)
(2) 图像模块: 采用TSM算法的特征层，提取时序图像的场景和时序特征(主要想获取借助工具等外力作弊的运动特征)
(3) 融合模块: 将(1)和(2)提取到的特征进行融合,以便获得1个特征输出
(3) 分类头；
重要说明【多模态特征具有互补性】
* 判断1个样本属于哪个类别，是同时需要骨骼特征和图像特征的，由于样本在送入模型时会抽样，3帧图像特征可能无法捕捉到到手未抱头的姿态，但更加密集的主体时序骨骼能够完美捕捉到，此是由时序骨骼特征判断；同理，主体的时序骨骼特征无法判断采用工具或人借力拉拽的方式作弊，但3帧时序的图像特征可以判断，此是由时序图像特征判断
  * 小结:
    未抱头行为检测：需要密集的17个关节点×35帧的骨骼序列来精确捕捉手部位置变化
    借助外力作弊检测：需要3帧关键时刻的RGB图像来识别工具、绳子、他人协助等视觉线索
    正常动作识别：需要骨骼+图像特征的联合判断来确保"全程双手抱头且无外力"



新的方案:
背景： 在原3分类基础上新增1个类别，该类别特点是数据量少(前3个类别平均5k+， 最后1个类别300份)，且分布杂
方案：基于异常检测（Anomaly Detection）的思路训练模型
这个方案的理论基础是，我们可以将数据充足的前三类视为“正常”样本，而将第四类视为“异常”样本。我们的目标是训练一个模型，使其能够精确地描绘出“正常”样本的边界。
核心思想：
训练一个深度神经网络，该网络不仅要学习区分前三类的分类边界，还要学习这三类样本在特征空间中的共同分布。任何偏离这个分布的样本都将被认为是异常的，即第四类。
具体实现：
基于重构误差（Reconstruction Error）的混合目标函数
模型结构：在你的分类模型主干网络（Backbone）之后，并联一个解码器（Decoder），构成一个自编码器（Autoencoder）结构。同时，原有的分类头（Classification Head）保持不变。
训练策略：
对于前三类的样本，模型需要同时优化两个损失函数：
分类损失（Classification Loss）: 如交叉熵损失，用于正确分类这三个类别。
重构损失（Reconstruction Loss）: 如均方误差（MSE），用于最小化输入样本经过编码器和解码器后的重构误差。
对于第四类的样本，只计算分类损失，不计算或极小化重构损失的权重。
理论依据：自编码器在训练时，会学习到“正常”数据（前三类）的本质压缩表示。当一个“正常”样本输入时，模型可以很好地重构它，重构误差很低。而当一个“异常”样本（第四类）输入时，由于模型没有学习过它的分布，重构出的样本会与原始输入有很大差异，导致重构误差很高。在训练中，通过将分类损失和重构损失结合，模型被迫将前三类样本映射到一个能够被解码器有效重构的潜在空间中，而第四类样本则被排斥在这个空间之外。
单类分类（One-Class Classification）思想的引入
模型结构：使用一个强大的特征提取网络。
训练策略：
首先，仅使用前三类的数据训练一个分类器，让模型学会区分这三类。
然后，固定特征提取网络，将其输出的特征向量视为输入，训练一个单类分类模型，如单类支持向量机（One-Class SVM）或深度支持向量数据描述（Deep SVDD）。[1][2] 这些模型的目标是学习一个能够紧密包裹住“正常”数据（前三类特征）的超球面。
在推理时，样本首先通过特征提取网络，然后判断其特征向量是否在超球面内。在球面外的样本被判定为第四类。
理论依据：这种方法将问题分解为“已知类分类”和“新颖点检测”两步。[3] 其优势在于逻辑清晰，但缺点是不是端到端的训练方式，可能会损失一部分性能。


需求：
1. 在默认配置文件基础上实现新的方案，新增配置文件、分类头等文件
2. 不改变原项目的配置和模型，新方案的改动必须通过**创建新文件**的方式实现



背景：
仔细阅读 @create_vidlabel_adaptive.py 的功能，该脚本在执行大量数据时，速度很慢
数据集每个类别可能包含多个子目录， 每个子目录中又可能包含子目录，需要的pkl样本存在于不同层级的子目录中
我在我真实的2W+份训练集上测试该脚本，总耗时457秒;

需求: 对该脚本进行优化，使其能够在3分钟左右完成所有样本的处理
我使用的环境是ubuntu系统，python3.8.17
要求: 优化的脚本以新建文件_trubo2命名，不要改变原脚本的内容
注意： 使用os.walk或pathlib遍历某个层级的所有pkl，这1方法被验证非常耗时，显示在终端中就是会卡顿在某个位置

**背景分析：**
请仔细分析 `create_vidlabel_adaptive.py` 脚本的功能和性能瓶颈。
- 已知性能瓶颈：使用 `os.walk()` 或 `pathlib` 遍历多层级目录结构寻找pkl文件的方法已被验证为主要耗时操作，会导致程序在某些位置出现明显卡顿

**优化需求：**
对该脚本进行性能优化，目标是将总处理时间从457秒缩短到180秒左右（3分钟以内）。

**技术要求：**
1. 运行环境：Ubuntu系统，Python 3.8.17
2. 创建优化版本：新建文件命名为 `create_vidlabel_turbo.py`
3. 保持原脚本不变：不要修改原始脚本 `create_vidlabel_adaptive.py` 的任何内容
4. 避免已知低效方法：不要使用 `os.walk()` 或 `pathlib` 的递归遍历方法来查找pkl文件

**优化方向建议：**
-参考@ReadTypeFile_Speed.py的方案

请先分析现有脚本的具体实现，识别性能瓶颈，然后提供具体的优化方案和实现。

在这个函数中增加1个可控参数insert_mod=True, 该模式对于帧数比要采样的帧数还少的情况，采取

1. 阅读 @README.md ，了解项目的基本功能；
2. 在最新版本上新增需求：
(1) 原本是3张图片, 后端拼接的样本图片新增1个骨骼图片，骨骼图片拼接方法如下:
a. 骨骼来源：每个样本文件夹中包含的1个pkl，该pkl文件的键'pred_skpts',对应的值是1个列表，列表中每个元素是1个骨骼列表，骨骼列表的第1个元素，即为需要的单帧骨骼目标, 获取所有骨骼的伪代码如下：
skpt_frame = [skpt_ts[0][6:].view(17,3)[:,:2] for skpt_ts in a_pkl['pred_skpts'] if skpt_ts is not None]
skpt_ts[0] 的格式是按照COCO数据集 17个骨骼关键点的顺序(YoloV8-pose模型的输出)
b. 将len(skpt_frame) 帧的所有骨骼全部绘制在1张1080p的黑色背景上；骨骼上色要求：每4帧采用同1中颜色，颜色由深色变浅
c. 按所有骨骼数据中的最大最小裁剪图片边界，并拼接到原方案3张图的最下方