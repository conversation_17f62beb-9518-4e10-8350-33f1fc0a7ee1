<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式作弊样本生成工具 v2.0</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>

<body>
    <h1>蒙板工具V2.0</h1>
    <div class="app-layout">
        <!-- 左侧控制面板 -->
        <div class="left-panel">
            <div class="controls">
                <h2>控制面板</h2>
                <div class="mode-selection">
                    <button id="mode-mask-btn" class="mode-btn active">模式一: 创建蒙版</button>
                    <button id="mode-gen-btn" class="mode-btn">模式二: 生成数据</button>
                </div>

                <div id="mask-creation-panel">
                    <div class="path-config-section">
                        <h4>路径配置</h4>
                        <div class="path-input-group">
                            <label for="mode1-source-path">源图片路径:</label>
                            <div class="path-input-container">
                                <input type="text" id="mode1-source-path" class="path-input" placeholder="选择源图片目录">
                                <button id="browse-mode1-source" class="browse-btn">浏览</button>
                            </div>
                        </div>
                    </div>

                    <h3>文件浏览器</h3>
                    <ul id="source-image-list" class="image-list">
                        <!-- 文件和文件夹列表将由JS动态加载 -->
                    </ul>
                    <button id="save-mask-btn" class="action-btn">保存蒙版</button>
                </div>

                <div id="data-generation-panel" class="hidden">
                    <div class="path-config-section">
                        <h4>路径配置</h4>
                        <div class="path-input-group">
                            <label for="mode2-input-path">输入数据路径:</label>
                            <div class="path-input-container">
                                <input type="text" id="mode2-input-path" class="path-input" placeholder="选择待处理样本目录">
                                <button id="browse-mode2-input" class="browse-btn">浏览</button>
                            </div>
                        </div>
                        <div class="path-input-group">
                            <label for="mode2-output-path">输出路径:</label>
                            <div class="path-input-container">
                                <input type="text" id="mode2-output-path" class="path-input" placeholder="自动根据输入路径设置"
                                    readonly>
                                <span class="path-info">将在输入路径同级创建Fake_AI目录</span>
                            </div>
                        </div>
                    </div>

                    <h3>工具蒙版列表 (Fake_tool_masks)</h3>
                    <ul id="tool-mask-list" class="image-list">
                        <!-- 蒙版列表将由JS动态加载 -->
                    </ul>
                    <h3>待处理样本 (0_normal)</h3>
                    <div id="progress-info"></div>
                    <ul id="normal-sample-list" class="image-list">
                        <!-- 样本列表将由JS动态加载 -->
                    </ul>
                    <button id="next-sample-btn" class="action-btn">处理下一个</button>
                </div>

                <div class="instructions">
                    <h3>操作说明</h3>
                    <p id="instruction-text">请在左侧文件浏览器中选择一张图片开始创建蒙版。</p>
                    <div class="hotkeys">
                        <h4>快捷键</h4>
                        <ul>
                            <li><kbd>Enter</kbd> - 完成多边形</li>
                            <li><kbd>Ctrl+Z</kbd> - 撤销上一个点</li>
                            <li><kbd>Escape</kbd> - 清空所有点</li>
                            <li><kbd>Ctrl+滚轮</kbd> - 缩放画布</li>
                            <li><kbd>Ctrl+拖动</kbd> - 平移画布</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中央画布区域 -->
        <div class="center-panel">
            <div class="canvas-container">
                <canvas id="main-canvas" width="800" height="600"></canvas>
            </div>

            <!-- 预览控制区域 -->
            <div id="preview-controls" class="hidden">
                <div class="preview-actions">
                    <button id="toggle-preview-btn" class="action-btn secondary">切换预览</button>
                    <button id="reset-preview-btn" class="action-btn secondary">显示原图</button>
                </div>
                <div id="preview-status" class="preview-status">
                    <span id="preview-status-text">准备预览</span>
                </div>
            </div>

            <div id="mask-preview-container">
                <h3>蒙版预览</h3>
                <img id="mask-preview-image" src="" alt="选中的蒙版预览" />
            </div>
        </div>

        <!-- 右侧参数面板 -->
        <div class="right-panel">
            <div id="parameter-panel" class="collapsed">
                <div class="panel-header">
                    <h3>参数调节</h3>
                    <button id="panel-toggle-btn" class="toggle-btn">展开</button>
                </div>

                <div class="panel-content">
                    <!-- 融合参数组 -->
                    <div class="param-group">
                        <h4>融合参数</h4>

                        <div class="param-item">
                            <label for="blend-strength">边缘融合强度:</label>
                            <div class="slider-container">
                                <input type="range" id="blend-strength" min="0" max="1" step="0.05" value="0.6">
                                <input type="number" id="blend-strength-input" min="0" max="1" step="0.05" value="0.6"
                                    class="param-input">
                            </div>
                        </div>

                        <div class="param-item">
                            <label for="fusion-mode">泊松融合模式:</label>
                            <select id="fusion-mode">
                                <option value="NORMAL_CLONE">NORMAL_CLONE</option>
                                <option value="MIXED_CLONE">MIXED_CLONE</option>
                            </select>
                        </div>

                        <div class="param-item">
                            <label for="seamless-weight">无痕融合权重:</label>
                            <div class="slider-container">
                                <input type="range" id="seamless-weight" min="0" max="1" step="0.05" value="0.6">
                                <input type="number" id="seamless-weight-input" min="0" max="1" step="0.05" value="0.6"
                                    class="param-input">
                            </div>
                        </div>
                    </div>

                    <!-- 颜色调整组 -->
                    <div class="param-group">
                        <h4>颜色调整</h4>

                        <div class="param-item">
                            <label for="mask-opacity">蒙版透明度:</label>
                            <div class="slider-container">
                                <input type="range" id="mask-opacity" min="0" max="1" step="0.05" value="1.0">
                                <input type="number" id="mask-opacity-input" min="0" max="1" step="0.05" value="1.0"
                                    class="param-input">
                            </div>
                        </div>

                        <div class="param-item">
                            <label for="brightness-adjust">亮度调整:</label>
                            <div class="slider-container">
                                <input type="range" id="brightness-adjust" min="-50" max="50" step="5" value="0">
                                <input type="number" id="brightness-adjust-input" min="-50" max="50" step="5" value="0"
                                    class="param-input">
                            </div>
                        </div>

                        <div class="param-item">
                            <label for="contrast-adjust">对比度调整:</label>
                            <div class="slider-container">
                                <input type="range" id="contrast-adjust" min="0.5" max="2.0" step="0.1" value="1.0">
                                <input type="number" id="contrast-adjust-input" min="0.5" max="2.0" step="0.1"
                                    value="1.0" class="param-input">
                            </div>
                        </div>
                    </div>

                    <!-- 预览设置组 -->
                    <div class="param-group">
                        <h4>预览设置</h4>

                        <div class="param-item">
                            <label for="realtime-preview">实时预览:</label>
                            <input type="checkbox" id="realtime-preview" checked>
                        </div>

                        <div class="param-item">
                            <label for="preview-size">预览尺寸:</label>
                            <select id="preview-size">
                                <option value="150">小 (150px)</option>
                                <option value="200" selected>中 (200px)</option>
                                <option value="300">大 (300px)</option>
                            </select>
                        </div>
                    </div>

                    <!-- 重置按钮 -->
                    <div class="param-actions">
                        <button id="reset-params-btn" class="action-btn secondary">重置参数</button>
                        <button id="save-preset-btn" class="action-btn secondary">保存预设</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div id="modal-overlay">
        <div id="confirmation-panel">
            <h3>生成结果预览</h3>
            <img id="result-image" src="" alt="生成结果" />
            <div class="confirmation-buttons">
                <button id="confirm-btn" class="action-btn">确认并继续</button>
                <button id="reject-btn" class="action-btn reject">拒绝并重做</button>
            </div>
        </div>
    </div>

    <!-- 目录浏览器模态对话框 -->
    <div id="directory-browser-modal" class="hidden">
        <div id="directory-browser-panel">
            <h3>选择目录</h3>
            <div class="current-path-display">
                <span>当前路径：</span>
                <span id="current-path">/</span>
            </div>
            <div class="directory-list-container">
                <ul id="directory-list" class="directory-list">
                    <!-- 目录列表将由JS动态加载 -->
                </ul>
            </div>
            <div class="browser-buttons">
                <button id="select-directory-btn" class="action-btn">选择此目录</button>
                <button id="cancel-browse-btn" class="action-btn secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 加载新的模块化脚本 -->
    <script src="{{ url_for('static', filename='src/managers/CoordinateManager.js') }}"></script>
    <script src="{{ url_for('static', filename='src/components/ParameterPanel.js') }}"></script>
    <script src="{{ url_for('static', filename='src/components/MainCanvasPreview.js') }}"></script>
    <script src="{{ url_for('static', filename='src/components/PathConfigManager.js') }}"></script>
    <script src="{{ url_for('static', filename='src/managers/ModeManager.js') }}"></script>
    <script src="{{ url_for('static', filename='src/utils/mathUtils.js') }}"></script>
    <script src="{{ url_for('static', filename='src/utils/imageUtils.js') }}"></script>
    <script src="{{ url_for('static', filename='src/app.js') }}"></script>
</body>

</html>