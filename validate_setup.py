#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
配置验证脚本
============

验证独立推理脚本的运行环境和配置
"""

import os
import sys
import json
import pickle
import importlib
from pathlib import Path
from typing import Dict, List, Tuple, Any

import torch
import numpy as np


class SetupValidator:
    """配置验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
    
    def add_error(self, message: str):
        """添加错误信息"""
        self.errors.append(f"❌ {message}")
    
    def add_warning(self, message: str):
        """添加警告信息"""
        self.warnings.append(f"⚠️  {message}")
    
    def add_info(self, message: str):
        """添加信息"""
        self.info.append(f"ℹ️  {message}")
    
    def print_results(self):
        """打印验证结果"""
        print("\n" + "="*60)
        print("配置验证结果")
        print("="*60)
        
        if self.info:
            print("\n📋 基本信息:")
            for info in self.info:
                print(f"  {info}")
        
        if self.warnings:
            print(f"\n⚠️  警告 ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if self.errors:
            print(f"\n❌ 错误 ({len(self.errors)}):")
            for error in self.errors:
                print(f"  {error}")
            print(f"\n请解决以上 {len(self.errors)} 个错误后再运行推理脚本。")
            return False
        else:
            print(f"\n✅ 验证通过! 可以运行独立推理脚本。")
            if self.warnings:
                print(f"注意: 有 {len(self.warnings)} 个警告，建议检查。")
            return True


def validate_python_environment(validator: SetupValidator):
    """验证Python环境"""
    print("检查Python环境...")
    
    # Python版本
    python_version = sys.version_info
    validator.add_info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        validator.add_error("Python版本过低，需要3.8或更高版本")
    
    # 检查必要的包
    required_packages = {
        'torch': 'PyTorch',
        'mmcv': 'MMCV',
        'mmengine': 'MMEngine', 
        'mmaction': 'MMAction2',
        'numpy': 'NumPy',
        'tqdm': 'tqdm',
        'PIL': 'Pillow'
    }
    
    missing_packages = []
    for package, name in required_packages.items():
        try:
            module = importlib.import_module(package)
            if hasattr(module, '__version__'):
                version = module.__version__
                validator.add_info(f"{name}: {version}")
            else:
                validator.add_info(f"{name}: 已安装")
        except ImportError:
            validator.add_error(f"缺少依赖包: {name} ({package})")
            missing_packages.append(package)
    
    if missing_packages:
        validator.add_error(f"请安装缺少的包: pip install {' '.join(missing_packages)}")


def validate_gpu_environment(validator: SetupValidator):
    """验证GPU环境"""
    print("检查GPU环境...")
    
    # CUDA可用性
    if torch.cuda.is_available():
        cuda_version = torch.version.cuda
        validator.add_info(f"CUDA版本: {cuda_version}")
        
        gpu_count = torch.cuda.device_count()
        validator.add_info(f"可用GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            validator.add_info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    else:
        validator.add_warning("CUDA不可用，将使用CPU推理（速度较慢）")


def validate_files(validator: SetupValidator, config_path: str = None, checkpoint_path: str = None, data_path: str = None):
    """验证文件和路径"""
    print("检查文件和路径...")
    
    # 检查脚本文件
    script_files = [
        'standalone_inference.py',
        'test_standalone_inference.py', 
        'example_usage.py',
        'quick_start.sh'
    ]
    
    for script in script_files:
        if os.path.exists(script):
            validator.add_info(f"脚本文件: {script} ✓")
        else:
            validator.add_warning(f"脚本文件不存在: {script}")
    
    # 检查配置文件
    if config_path:
        if os.path.exists(config_path):
            validator.add_info(f"配置文件: {config_path} ✓")
            
            # 尝试加载配置文件
            try:
                from mmengine.config import Config
                cfg = Config.fromfile(config_path)
                validator.add_info("配置文件格式正确")
                
                # 检查关键配置
                if hasattr(cfg, 'model'):
                    validator.add_info("模型配置存在")
                else:
                    validator.add_error("配置文件缺少模型配置")
                
                if hasattr(cfg, 'test_pipeline'):
                    validator.add_info("测试管道配置存在")
                else:
                    validator.add_error("配置文件缺少测试管道配置")
                    
            except Exception as e:
                validator.add_error(f"配置文件加载失败: {e}")
        else:
            validator.add_error(f"配置文件不存在: {config_path}")
    
    # 检查模型权重文件
    if checkpoint_path:
        if os.path.exists(checkpoint_path):
            validator.add_info(f"模型权重文件: {checkpoint_path} ✓")
            
            # 检查文件大小
            file_size = os.path.getsize(checkpoint_path) / 1024**2  # MB
            validator.add_info(f"模型文件大小: {file_size:.1f}MB")
            
            if file_size < 1:
                validator.add_warning("模型文件过小，可能不完整")
        else:
            validator.add_error(f"模型权重文件不存在: {checkpoint_path}")
    
    # 检查数据路径
    if data_path:
        if os.path.exists(data_path):
            validator.add_info(f"数据路径: {data_path} ✓")
            
            # 统计数据文件
            data_path_obj = Path(data_path)
            pkl_files = list(data_path_obj.rglob("*.pkl"))
            jpg_files = list(data_path_obj.rglob("*.jpg"))
            
            validator.add_info(f"PKL文件数量: {len(pkl_files)}")
            validator.add_info(f"JPG文件数量: {len(jpg_files)}")
            
            if len(pkl_files) == 0:
                validator.add_error("数据目录中没有找到PKL文件")
            elif len(pkl_files) < 10:
                validator.add_warning(f"PKL文件数量较少 ({len(pkl_files)})，可能不足以进行有效测试")
            
            # 检查数据格式
            if pkl_files:
                sample_pkl = pkl_files[0]
                try:
                    with open(sample_pkl, 'rb') as f:
                        data = pickle.load(f)
                    
                    if 'pred_skpts' in data:
                        validator.add_info("PKL文件格式正确（包含pred_skpts）")
                    else:
                        validator.add_error("PKL文件格式错误（缺少pred_skpts字段）")
                        
                except Exception as e:
                    validator.add_error(f"无法读取PKL文件: {e}")
        else:
            validator.add_error(f"数据路径不存在: {data_path}")


def validate_data_format(validator: SetupValidator, data_path: str):
    """验证数据格式"""
    if not data_path or not os.path.exists(data_path):
        return
    
    print("检查数据格式...")
    
    data_path_obj = Path(data_path)
    pkl_files = list(data_path_obj.rglob("*.pkl"))
    
    if not pkl_files:
        return
    
    # 检查前几个样本的数据格式
    sample_count = min(3, len(pkl_files))
    valid_samples = 0
    
    for i, pkl_file in enumerate(pkl_files[:sample_count]):
        try:
            with open(pkl_file, 'rb') as f:
                data = pickle.load(f)
            
            # 检查必要字段
            required_fields = ['pred_skpts', 'sample_name']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                validator.add_warning(f"样本 {pkl_file.name} 缺少字段: {missing_fields}")
            else:
                valid_samples += 1
            
            # 检查关键点数据
            if 'pred_skpts' in data:
                keypoints = data['pred_skpts']
                if isinstance(keypoints, list) and len(keypoints) > 0:
                    validator.add_info(f"样本 {pkl_file.name}: {len(keypoints)} 帧")
                else:
                    validator.add_warning(f"样本 {pkl_file.name} 关键点数据为空")
            
            # 检查对应的图像文件
            base_name = pkl_file.stem
            parent_dir = pkl_file.parent
            jpg_count = 0
            
            for j in range(10):
                jpg_path = parent_dir / f"{base_name}-{j}.jpg"
                if jpg_path.exists():
                    jpg_count += 1
                else:
                    break
            
            if jpg_count >= 3:
                validator.add_info(f"样本 {pkl_file.name}: {jpg_count} 张图像")
            else:
                validator.add_warning(f"样本 {pkl_file.name} 图像文件不足 ({jpg_count})")
                
        except Exception as e:
            validator.add_error(f"读取样本 {pkl_file.name} 失败: {e}")
    
    if valid_samples > 0:
        validator.add_info(f"数据格式检查: {valid_samples}/{sample_count} 个样本格式正确")
    else:
        validator.add_error("没有找到格式正确的数据样本")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='验证独立推理脚本的运行环境')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--checkpoint', type=str, help='模型权重文件路径')
    parser.add_argument('--data_path', type=str, help='测试数据路径')
    parser.add_argument('--quick', action='store_true', help='快速检查（跳过数据格式验证）')
    
    args = parser.parse_args()
    
    print("独立推理脚本配置验证")
    print("=" * 30)
    
    validator = SetupValidator()
    
    # 验证Python环境
    validate_python_environment(validator)
    
    # 验证GPU环境
    validate_gpu_environment(validator)
    
    # 验证文件
    validate_files(validator, args.config, args.checkpoint, args.data_path)
    
    # 验证数据格式（如果不是快速模式）
    if not args.quick and args.data_path:
        validate_data_format(validator, args.data_path)
    
    # 打印结果
    success = validator.print_results()
    
    if success:
        print("\n🚀 推荐的运行命令:")
        if args.config and args.checkpoint and args.data_path:
            print(f"python standalone_inference.py \\")
            print(f"    --data_path {args.data_path} \\")
            print(f"    --config {args.config} \\")
            print(f"    --checkpoint {args.checkpoint} \\")
            print(f"    --batch_size 16 \\")
            print(f"    --device cuda:0 \\")
            print(f"    --output_file results.json")
        else:
            print("python standalone_inference.py --help")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
