#!/bin/bash
# 独立推理脚本快速启动脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查文件是否存在
check_file() {
    if [ -f "$1" ]; then
        print_success "找到文件: $1"
        return 0
    else
        print_error "文件不存在: $1"
        return 1
    fi
}

# 检查目录是否存在
check_dir() {
    if [ -d "$1" ]; then
        print_success "找到目录: $1"
        return 0
    else
        print_error "目录不存在: $1"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "独立推理脚本快速启动工具"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --data PATH        测试数据路径"
    echo "  -c, --config PATH      配置文件路径"
    echo "  -k, --checkpoint PATH  模型权重文件路径"
    echo "  -b, --batch_size N     批处理大小 (默认: 16)"
    echo "  -g, --gpu DEVICE       GPU设备 (默认: cuda:0, 支持多GPU如: 4,5,6,7)"
    echo "  -o, --output FILE      输出文件 (默认: results.json)"
    echo "  -m, --multi_gpu        启用多GPU推理"
    echo "  -t, --test             运行测试模式"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -d /path/to/data -c config.py -k model.pth"
    echo "  $0 -d /path/to/data -c config.py -k model.pth -b 32 -m"
    echo "  $0 -t  # 运行测试"
}

# 默认参数
DATA_PATH=""
CONFIG_PATH="configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py"
CHECKPOINT_PATH=""
BATCH_SIZE=16
DEVICE="cuda:0"
OUTPUT_FILE="results.json"
USE_MULTI_GPU=false
TEST_MODE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--data)
            DATA_PATH="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_PATH="$2"
            shift 2
            ;;
        -k|--checkpoint)
            CHECKPOINT_PATH="$2"
            shift 2
            ;;
        -b|--batch_size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        -g|--gpu)
            DEVICE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -m|--multi_gpu)
            USE_MULTI_GPU=true
            shift
            ;;
        -t|--test)
            TEST_MODE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    print_info "独立推理脚本快速启动"
    echo "=================================="
    
    # 测试模式
    if [ "$TEST_MODE" = true ]; then
        print_info "运行测试模式..."
        
        if [ -f "test_standalone_inference.py" ]; then
            python test_standalone_inference.py
        else
            print_error "测试脚本不存在: test_standalone_inference.py"
            exit 1
        fi
        
        print_success "测试完成"
        exit 0
    fi
    
    # 检查必需参数
    if [ -z "$DATA_PATH" ]; then
        print_error "请指定数据路径 (-d/--data)"
        show_help
        exit 1
    fi
    
    if [ -z "$CHECKPOINT_PATH" ]; then
        print_error "请指定模型权重文件 (-k/--checkpoint)"
        show_help
        exit 1
    fi
    
    # 检查文件和目录
    print_info "检查文件和目录..."
    
    check_dir "$DATA_PATH" || exit 1
    check_file "$CONFIG_PATH" || exit 1
    check_file "$CHECKPOINT_PATH" || exit 1
    check_file "standalone_inference.py" || exit 1
    
    # 检查Python环境
    print_info "检查Python环境..."
    
    if ! command -v python &> /dev/null; then
        print_error "Python未安装或不在PATH中"
        exit 1
    fi
    
    python_version=$(python --version 2>&1)
    print_success "Python版本: $python_version"
    
    # 检查GPU（如果使用CUDA）
    if [[ "$DEVICE" == cuda* ]]; then
        print_info "检查GPU环境..."
        
        if command -v nvidia-smi &> /dev/null; then
            gpu_info=$(nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits | head -1)
            print_success "GPU信息: $gpu_info"
        else
            print_warning "nvidia-smi未找到，无法检查GPU状态"
        fi
    fi
    
    # 显示配置信息
    print_info "推理配置:"
    echo "  数据路径: $DATA_PATH"
    echo "  配置文件: $CONFIG_PATH"
    echo "  模型权重: $CHECKPOINT_PATH"
    echo "  批处理大小: $BATCH_SIZE"
    echo "  设备: $DEVICE"
    echo "  输出文件: $OUTPUT_FILE"
    echo "  多GPU: $USE_MULTI_GPU"
    
    # 构建命令
    cmd="python standalone_inference.py"
    cmd="$cmd --data_path \"$DATA_PATH\""
    cmd="$cmd --config \"$CONFIG_PATH\""
    cmd="$cmd --checkpoint \"$CHECKPOINT_PATH\""
    cmd="$cmd --batch_size $BATCH_SIZE"
    cmd="$cmd --device $DEVICE"
    cmd="$cmd --output_file \"$OUTPUT_FILE\""
    
    if [ "$USE_MULTI_GPU" = true ]; then
        cmd="$cmd --use_multi_gpu"
    fi
    
    print_info "执行命令:"
    echo "$cmd"
    
    # 询问用户确认
    echo ""
    read -p "是否开始推理? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "取消执行"
        exit 0
    fi
    
    # 执行推理
    print_info "开始推理..."
    start_time=$(date +%s)
    
    if eval "$cmd"; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        print_success "推理完成!"
        print_info "总耗时: ${duration}秒"
        
        # 检查结果文件
        if [ -f "$OUTPUT_FILE" ]; then
            print_success "结果已保存到: $OUTPUT_FILE"
            
            # 显示结果摘要
            if command -v jq &> /dev/null; then
                print_info "结果摘要:"
                total=$(jq '.statistics.total_samples' "$OUTPUT_FILE" 2>/dev/null || echo "N/A")
                successful=$(jq '.statistics.successful_samples' "$OUTPUT_FILE" 2>/dev/null || echo "N/A")
                failed=$(jq '.statistics.failed_samples' "$OUTPUT_FILE" 2>/dev/null || echo "N/A")
                
                echo "  总样本数: $total"
                echo "  成功样本数: $successful"
                echo "  失败样本数: $failed"
                
                if [ "$total" != "N/A" ] && [ "$total" -gt 0 ]; then
                    success_rate=$(echo "scale=2; $successful * 100 / $total" | bc 2>/dev/null || echo "N/A")
                    echo "  成功率: ${success_rate}%"
                fi
            else
                print_info "安装jq可以查看详细的结果摘要: sudo apt-get install jq"
            fi
        else
            print_warning "结果文件未找到: $OUTPUT_FILE"
        fi
        
    else
        print_error "推理执行失败!"
        exit 1
    fi
}

# 运行主函数
main "$@"
