# _base_ = '../../_base_/default_runtime.py'          # _base_ 会继承一些通用设置，例如学习率调度器，优化器等
import os
default_scope = 'mmaction'

default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=20, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=1, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)

vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(type='ActionVisualizer', vis_backends=vis_backends)

log_level = 'INFO'
load_from = None
resume = False

# ====================================================================================================================================


# 基础配置
num_classes = 3         # 类别数
split_label = {0:[1,2], 1:[0]}       # 0:异常, 1:正常


# 模型配置
model = dict(
    type='MultiModalRecognizer',
    pose_backbone=dict(
        type='STGCN',  # 或其他姿态骨干网络 PoseC3D
        in_channels=3,          # xyc * ['j', 'b']           num_class=256
        graph_cfg=dict(
            layout='coco',        # COCO 17个关键点
            mode='stgcn_spatial'),
        init_cfg=dict(type='Pretrained', checkpoint='../stgcn_8xb16-bone-u100-80e_ntu120-xsub-keypoint-2d_20221129-131e63c3.pth')
    ),
    image_backbone=dict(
        type='MobileNetV2TSM',
        # --- MobileNetV2TSM 专属参数 ---
        num_segments=3,
        is_shift=True,
        shift_div=8,
        pretrained2d=False,
        init_cfg=dict(type='Pretrained', checkpoint='../tsm_imagenet-pretrained-mobilenetv2_8xb16-1x1x8-100e_kinetics400-rgb_20230414-401127fd.pth'),
        # 使用模型自带的2D预训练加载逻辑
        # 当 pretrained2d=True 时，init_weights会使用这个路径 mmcls://mobilenet_v2
        out_indices=(6,),
        frozen_stages=-1,
        norm_eval=False, ),
    fusion_neck=dict(
        type='MultiModalFusionNeck',        # 适配器
        pose_feat_dim=256,              # ST-GCN 输出维度(num_class=256)
        img_feat_dim=320,              # TSM为1280
        fusion_dim=512,
        fusion_type='attention',  # 跨模态融合cross_modal < attention
        dropout=0.5
    ),
    cls_head=dict(
        type='TwoFusionHead',              # 双流融合特征分类头
        num_classes=num_classes,              # 类别
        in_channels=512,
        loss_cls=dict(
            type='CrossEntropyLoss',
            loss_weight=1.0,
            class_weight=[1.0] * num_classes  # TODO 类别权重
            # class_weight=[1.0,1.5,1.0]  # TODO 类别权重
        )
    ),
    train_cfg=None,
    test_cfg=dict(average_clips='prob')
)

# 数据集配置
dataset_type = 'PoseRgbDataset'
TrainVal_ann_file = '/media/pyl/WD_Blue_1T/All_proj/classify_cheat/Fusion_datasets/trainval_yolopose.pkl'
Test_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl'
# Test_ann_file = '/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/2_headErr/Jiangsu_nanjingjinlingzhongxuehexixiaoqu/../Fusion_datasets/test_all_label_ann.pkl'


train_pipeline = [
    # 归一化：将坐标从像素空间 [0, W] 和 [0, H] 映射到了归一化空间 [-1, 1]； 中心化：将所有点的坐标变为相对于“髋部中心点”的坐标。
    dict(type='fusionPreNormalize2D', left_idx=11, right_idx=12),
    # feats=['b']: JointToBone
    dict(type='GenSkeFeat', dataset='coco', feats=['b']),      # 2S-GCN ['j','b', 'bm', 'jm']
    dict(type='UniformSampleFrames', clip_len=35, seed=255),        # 均匀采样
    dict(type='SimplifiedFormatGCNInput'),
    # 随机对骨架进行仿射变换（旋转、平移、缩放）。这是最核心的数据增强。 于你的动作尺度和位置变化不大，可以适当减小变换范围
    # dict(type='GeneratePoseTarget', sigma=0.6, use_score=True, with_kp=True, with_limb=False),

    # Way1： load_Resized_img=True, 表示将Resize 放到本地已经完成，可以直接加载(224,)的图片
    # dict(type='SimplyResize', scale=(-1, 256), load_Resized_img=True),         # 保持图像的宽高比，将图像的最短边缩放到256像素，长边则等比例缩放
    # Way2： load_Resized_img=False, 下面2行都要打开！！！
    # dict(type='SimplyResize', scale=(-1, 256), load_Resized_img=False),  # 保持图像的宽高比，将图像的最短边缩放到256像素，长边则等比例缩放
    # dict(type='SimplyResize', scale=(224, 224), keep_ratio=False),

    # 策略3： Padding
    dict(type='SimplyResize', scale=(224, 224), use_padding=True, pad_val=(128, 128, 128), load_Resized_img=True),


    dict(type='ColorJitter'),
    dict(type='Flip', flip_ratio=0.5),          # TODO 同时会翻转预处理后的骨骼
    dict(type='FormatShape', input_format='NCHW'),

    # 打包成模型输入的字典
    dict(type='PackActionInputs', collect_keys=('keypoint', 'imgs')),

]

val_pipeline = [
    dict(type='fusionPreNormalize2D', left_idx=11, right_idx=12),   # 归一化、中心化
    dict(type='GenSkeFeat', dataset='coco', feats=['b']),      # 2S-GCN ['j','b', 'bm', 'jm']
    dict(type='UniformSampleFrames', clip_len=35, test_mode=True),        # 均匀采样
    dict(type='SimplifiedFormatGCNInput'),

    # Way1：
    # dict(type='SimplyResize', scale=(-1, 256), load_Resized_img=True),  # Load Resized Img (224,)

    # Way2：
    # dict(type='SimplyResize', scale=(-1, 256), load_Resized_img=False),  # 保持图像的宽高比，将图像的最短边缩放到256像素，长边则等比例缩放
    # dict(type='SimplyResize', scale=(224, 224), keep_ratio=False),

    # 策略3 padding
    dict(type='SimplyResize', scale=(224, 224), use_padding=True, pad_val=(128, 128, 128), load_Resized_img=True),

    dict(type='FormatShape', input_format='NCHW'),
    dict(type='PackActionInputs', collect_keys=('keypoint', 'imgs')),
]

test_pipeline = [
    dict(type='fusionPreNormalize2D', left_idx=11, right_idx=12),   # 归一化、中心化
    dict(type='GenSkeFeat', dataset='coco', feats=['b']),      # 2S-GCN ['j','b', 'bm', 'jm']
    dict(type='UniformSampleFrames', clip_len=35, test_mode=True),        # 均匀采样
    dict(type='SimplifiedFormatGCNInput'),

    # Way2:
    # dict(type='SimplyResize', scale=(-1, 256)),                     # 保持图像的宽高比，将图像的最短边缩放到256像素，长边则等比例缩放
    # dict(type='SimplyResize', scale=(224, 224), keep_ratio=False),

    # 策略3 padding
    dict(type='SimplyResize', scale=(224, 224), use_padding=True, pad_val=(128, 128, 128)),

    # dict(type='ColorJitter'),                   # TODO 验证 一致性正则化
    # dict(type='Flip', flip_ratio=0.5),
    dict(type='FormatShape', input_format='NCHW'),
    dict(type='PackActionInputs_Test', collect_keys=('keypoint', 'imgs'),
         pred_txt=os.path.dirname(Test_ann_file), split_label=split_label),
]

# 训练参数配置
train_dataloader = dict(
    batch_size=4,
    num_workers=0,
    # persistent_workers=True,
    # pin_memory=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split='train',
        pipeline=train_pipeline))
val_dataloader = dict(
    batch_size=4,
    num_workers=0,
    # pin_memory=True,
    # persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=TrainVal_ann_file,
        split='val',
        pipeline=val_pipeline,
        test_mode=True))
test_dataloader = dict(
    batch_size=64,
    num_workers=8,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=Test_ann_file,         # test_trainval_yolopose.pkl
        split=None,                 # 不进行数据划分
        pipeline=test_pipeline,
        test_mode=True))

val_evaluator = [dict(type='AccMetric')]
test_evaluator = [dict(type='ClassifyReport')]          # 自定义分类报告 ClassifyReport

train_cfg = dict(
    type='EpochBasedTrainLoop', max_epochs=100, val_begin=1, val_interval=1)     # 按轮次(Epoch)训练
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')                        # TestLoop



optim_wrapper = dict(
    # optim_wrapper的类型，通常是'OptimWrapper'或用于混合精度的'AmpOptimWrapper'
    type='OptimWrapper',        # 默认为 'OptimWrapper'

    # optimizer字典只包含torch.optim.AdamW本身接受的参数
    optimizer=dict(
        type='AdamW',
        lr=0.001,  # 基础学习率
        betas=(0.9, 0.999),
        weight_decay=0.0001
    ),

    # paramwise_cfg 与 optimizer 并列，用于指导 OptimWrapper 如何构建参数组
    paramwise_cfg=dict(
        custom_keys={   # key 是模型中模块的属性名 (e.g., self.image_backbone)
            'image_backbone': dict(lr_mult=0.1),  # 学习率 = 基础学习率 * 0.1
            'pose_backbone': dict(lr_mult=1.0),     # 其他模块使用基础学习率，可以不写，因为默认 lr_mult=1.0
            'fusion_neck': dict(lr_mult=1.0),       # 融合模块 默认学习率
            'cls_head': dict(lr_mult=1.0)           # 分类头 默认学习率
        }
    ),

    clip_grad=dict(max_norm=20, norm_type=2),        # 梯度裁剪配置也属于 optim_wrapper

)



# 假设总共训练 100 个 epochs，warmup 占 5 个 epochs
param_scheduler = [
    # 线性预热
    dict(
        type='LinearLR',
        start_factor=0.01,
        by_epoch=True,  # 按 Epoch 计数
        begin=0,
        end=5           # 预热 5 个 epochs
    ),
    # 余弦退火
    dict(
        type='CosineAnnealingLR',
        T_max=95,       # 100 (max_epochs) - 5 (warmup_epochs)
        eta_min=1e-6,
        by_epoch=True,  # 按 Epoch 计数
        begin=5,
        end=100
    )
]

work_dir = './work_dirs/pose_rgb_fusion'

find_unused_parameters = True

# Optuna Trial 9 Configuration

optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(
        type='AdamW',
        lr=1.863797807124112e-05,
        betas=(0.9, 0.999),
        weight_decay=0.0003214461717569222
    ),
    paramwise_cfg=dict(
        custom_keys={
            'image_backbone': dict(lr_mult=0.17400277071326165),
            'pose_backbone': dict(lr_mult=1.0),
            'fusion_neck': dict(lr_mult=0.6687634135195245),
            'cls_head': dict(lr_mult=1.6635993174713428)
        }
    ),
    clip_grad=dict(max_norm=20, norm_type=2)
)


# 更新模型配置
model.update(dict(
    fusion_neck=dict(
        type='MultiModalFusionNeck',
        pose_feat_dim=256,
        img_feat_dim=320,
        fusion_dim=512,
        fusion_type='attention',
        dropout=0.6241870687449977
    )
))


# 更新数据加载器配置
train_dataloader.update(dict(batch_size=6))
val_dataloader.update(dict(batch_size=6))


# 学习率调度器配置
param_scheduler = [
    dict(
        type='LinearLR',
        start_factor=0.01,
        by_epoch=True,
        begin=0,
        end=6
    ),
    dict(
        type='CosineAnnealingLR',
        T_max=94,
        eta_min=1e-6,
        by_epoch=True,
        begin=6,
        end=100
    )
]

work_dir = './work_dirs/mini_optuna_test/trial_9'

# Optuna Hook Configuration
custom_hooks = [
    dict(
        type='OptunaHook',
        monitor_metric='val/acc',
        pruning_enabled=True,
        min_epochs=10,
        patience=5
    )
]

