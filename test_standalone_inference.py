#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
测试独立推理脚本
================

用于测试standalone_inference.py的功能
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path
import numpy as np
import pickle
import torch
from PIL import Image

def create_dummy_data(output_dir: str, num_samples: int = 5):
    """创建虚拟测试数据"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"创建 {num_samples} 个虚拟样本到 {output_dir}")
    
    for i in range(num_samples):
        sample_name = f"sample_{i:03d}"
        sample_dir = output_path / "test_class" / f"subdir_{i}"
        sample_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建虚拟pkl文件
        pkl_path = sample_dir / f"{sample_name}.pkl"
        
        # 创建虚拟关键点数据
        num_frames = np.random.randint(10, 50)
        keypoint_data = []
        
        for frame_idx in range(num_frames):
            # 模拟YOLO pose输出格式：前6个是其他信息，后面是17个关键点*3(x,y,conf)
            frame_data = np.random.rand(6 + 17 * 3) * 100
            # 转换为torch tensor格式（模拟真实数据）
            tensor_data = torch.tensor(frame_data, dtype=torch.float32)
            keypoint_data.append([tensor_data])
        
        pkl_data = {
            'pred_skpts': keypoint_data,
            'sample_name': sample_name,
            'img_shape': (1080, 1920),
            'oriVid_shape': (1080, 1920),
            'total_frames': num_frames
        }
        
        # 保存pkl文件
        with open(pkl_path, 'wb') as f:
            pickle.dump(pkl_data, f)
        
        # 创建对应的jpg文件
        for j in range(3):  # 创建3张图片
            img_path = sample_dir / f"{sample_name}-{j}.jpg"
            # 创建随机图像
            img = Image.fromarray(np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8))
            img.save(img_path)
        
        print(f"创建样本: {sample_name} (包含 {num_frames} 帧)")
    
    print("虚拟数据创建完成!")
    return str(output_path)


def test_data_loading():
    """测试数据加载功能"""
    print("\n=== 测试数据加载功能 ===")
    
    # 创建临时数据
    with tempfile.TemporaryDirectory() as temp_dir:
        data_path = create_dummy_data(temp_dir, num_samples=3)
        
        # 测试数据扫描
        from standalone_inference import StandaloneDataLoader
        from mmengine.dataset import Compose
        
        # 创建简单的预处理管道用于测试
        test_pipeline = Compose([])
        
        try:
            data_loader = StandaloneDataLoader(
                data_path=data_path,
                test_pipeline=test_pipeline,
                batch_size=2
            )
            
            print(f"扫描到 {len(data_loader)} 个样本")
            
            # 测试数据构造
            for i, file_info in enumerate(data_loader.data_files[:2]):  # 只测试前2个
                print(f"\n测试样本 {i+1}:")
                print(f"  PKL路径: {file_info['pkl_path']}")
                print(f"  JPG文件数: {len(file_info['jpg_paths'])}")
                
                # 测试数据字典构造
                data_dict = data_loader._construct_data_dict(file_info)
                if data_dict:
                    print(f"  关键点形状: {data_dict['keypoint'].shape}")
                    print(f"  分数形状: {data_dict['keypoint_score'].shape}")
                    print(f"  总帧数: {data_dict['total_frames']}")
                    print("  ✓ 数据构造成功")
                else:
                    print("  ✗ 数据构造失败")
            
            print("\n数据加载测试完成!")
            
        except Exception as e:
            print(f"数据加载测试失败: {e}")
            return False
    
    return True


def test_inference_engine():
    """测试推理引擎（需要真实的配置文件和模型）"""
    print("\n=== 测试推理引擎 ===")
    
    # 检查配置文件是否存在
    config_path = "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py"
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        print("跳过推理引擎测试")
        return True
    
    # 这里需要真实的checkpoint文件，所以只做基本检查
    print("推理引擎测试需要真实的模型文件，跳过详细测试")
    print("请使用真实数据和模型运行完整测试")
    
    return True


def test_command_line():
    """测试命令行接口"""
    print("\n=== 测试命令行接口 ===")
    
    # 创建临时数据
    with tempfile.TemporaryDirectory() as temp_dir:
        data_path = create_dummy_data(temp_dir, num_samples=2)
        
        # 构造测试命令
        cmd_args = [
            "--data_path", data_path,
            "--config", "configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py",
            "--checkpoint", "dummy_checkpoint.pth",  # 虚拟路径
            "--batch_size", "2",
            "--device", "cpu",
            "--output_file", os.path.join(temp_dir, "test_results.json"),
            "--log_level", "INFO"
        ]
        
        print(f"测试命令参数: {' '.join(cmd_args)}")
        
        # 测试参数解析
        try:
            import sys
            old_argv = sys.argv
            sys.argv = ["standalone_inference.py"] + cmd_args
            
            from standalone_inference import parse_args
            args = parse_args()
            
            print("✓ 命令行参数解析成功")
            print(f"  数据路径: {args.data_path}")
            print(f"  批处理大小: {args.batch_size}")
            print(f"  设备: {args.device}")
            
            sys.argv = old_argv
            
        except Exception as e:
            print(f"✗ 命令行参数解析失败: {e}")
            return False
    
    return True


def run_integration_test():
    """运行集成测试（需要真实环境）"""
    print("\n=== 集成测试说明 ===")
    print("要运行完整的集成测试，请执行以下命令：")
    print()
    print("python standalone_inference.py \\")
    print("    --data_path /path/to/your/test/data \\")
    print("    --config configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py \\")
    print("    --checkpoint /path/to/your/checkpoint.pth \\")
    print("    --batch_size 16 \\")
    print("    --device cuda:0 \\")
    print("    --output_file results.json \\")
    print("    --use_multi_gpu")
    print()
    print("确保：")
    print("1. 数据目录包含pkl文件和对应的jpg文件")
    print("2. 配置文件路径正确")
    print("3. 模型权重文件存在")
    print("4. GPU设备可用（如果使用GPU）")


def main():
    """主测试函数"""
    print("开始测试独立推理脚本...")
    
    tests = [
        ("数据加载", test_data_loading),
        ("推理引擎", test_inference_engine),
        ("命令行接口", test_command_line),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✓ 通过" if result else "✗ 失败"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            print(f"\n{test_name}: ✗ 异常 - {e}")
            results.append((test_name, False))
    
    # 打印测试总结
    print(f"\n{'='*50}")
    print("测试总结")
    print('='*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    # 显示集成测试说明
    run_integration_test()


if __name__ == "__main__":
    main()
