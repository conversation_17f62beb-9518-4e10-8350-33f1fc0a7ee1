#!/usr/bin/env python3
"""
训练日志指标绘制脚本
解析MMAction2训练日志，绘制训练/验证指标和学习率曲线图

使用方法:
python plot_training_metrics.py /path/to/log/file.log
"""

import re
import os
import sys
import argparse
import json
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import matplotlib.ticker as ticker
from typing import Dict, List, Tuple, Optional
import numpy as np

# 设置字体和后端
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False

class TrainingLogParser:
    """训练日志解析器"""
    
    def __init__(self, log_path: str):
        self.log_path = log_path
        self.log_dir = os.path.dirname(log_path)
        
        # 存储解析的数据
        self.epochs = []
        self.train_acc = []
        self.train_loss = []
        self.val_acc = []
        self.val_loss = []
        self.learning_rates = []

        # 🔥 新增：vis_data数据存储
        self.vis_data_available = False
        self.vis_train_acc = []
        self.vis_train_loss = []
        self.vis_val_acc = []
        self.vis_learning_rates = []
        self.vis_grad_norm = []
        self.vis_epochs = []
        
        # 正则表达式模式
        self.train_pattern = re.compile(
            r'Epoch\(train\)\s+\[(\d+)\]\[\s*\d+/\d+\].*?'
            r'lr:\s*([\d\.e\-\+]+).*?'
            r'loss:\s*([\d\.]+).*?'
            r'top1_acc:\s*([\d\.]+)'
        )

        self.val_pattern = re.compile(
            r'Epoch\(val\)\s+\[(\d+)\]\[\s*\d+/\d+\].*?'
            r'acc/top1:\s*([\d\.]+)'
        )

        # 🔥 新增：验证损失模式（如果存在的话）
        self.val_loss_pattern = re.compile(
            r'Epoch\(val\)\s+\[(\d+)\]\[\s*\d+/\d+\].*?'
            r'loss:\s*([\d\.]+).*?'
            r'acc/top1:\s*([\d\.]+)'
        )
    
    def parse_log(self) -> bool:
        """解析日志文件"""
        try:
            with open(self.log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析训练数据 - 只取每个epoch的最后一条记录
            train_matches = self.train_pattern.findall(content)
            val_matches = self.val_pattern.findall(content)
            val_loss_matches = self.val_loss_pattern.findall(content)

            print(f"找到 {len(train_matches)} 条训练记录")
            print(f"找到 {len(val_matches)} 条验证记录")
            print(f"找到 {len(val_loss_matches)} 条验证损失记录")

            # 处理训练数据 - 按epoch分组，取每个epoch的最后一条
            train_data_by_epoch = {}
            for match in train_matches:
                epoch, lr, loss, acc = match
                epoch = int(epoch)
                train_data_by_epoch[epoch] = {
                    'lr': float(lr),
                    'loss': float(loss),
                    'acc': float(acc)
                }

            # 处理验证数据（准确率）
            val_data_by_epoch = {}
            for match in val_matches:
                epoch, acc = match
                epoch = int(epoch)
                val_data_by_epoch[epoch] = {'acc': float(acc)}

            # 🔥 处理验证损失数据
            val_loss_by_epoch = {}
            for match in val_loss_matches:
                epoch, loss, acc = match
                epoch = int(epoch)
                val_loss_by_epoch[epoch] = float(loss)
                # 同时更新验证准确率数据
                if epoch in val_data_by_epoch:
                    val_data_by_epoch[epoch]['loss'] = float(loss)
                else:
                    val_data_by_epoch[epoch] = {'acc': float(acc), 'loss': float(loss)}
            
            # 合并数据
            all_epochs = sorted(set(train_data_by_epoch.keys()) | set(val_data_by_epoch.keys()))
            
            for epoch in all_epochs:
                self.epochs.append(epoch)
                
                # 训练数据
                if epoch in train_data_by_epoch:
                    train_data = train_data_by_epoch[epoch]
                    self.train_acc.append(train_data['acc'])
                    self.train_loss.append(train_data['loss'])
                    self.learning_rates.append(train_data['lr'])
                else:
                    # 如果某个epoch没有训练数据，用前一个值填充
                    self.train_acc.append(self.train_acc[-1] if self.train_acc else 0)
                    self.train_loss.append(self.train_loss[-1] if self.train_loss else 0)
                    self.learning_rates.append(self.learning_rates[-1] if self.learning_rates else 0)
                
                # 验证数据
                if epoch in val_data_by_epoch:
                    val_data = val_data_by_epoch[epoch]
                    self.val_acc.append(val_data['acc'])
                    # 🔥 改进3: 添加验证损失支持
                    self.val_loss.append(val_data.get('loss', None))
                else:
                    self.val_acc.append(None)
                    self.val_loss.append(None)
            
            print(f"成功解析日志文件: {self.log_path}")
            print(f"解析到 {len(self.epochs)} 个epoch的数据")
            return True
            
        except Exception as e:
            print(f"解析日志文件失败: {e}")
            return False

    def parse_vis_data(self) -> bool:
        """解析vis_data中的scalars.json文件"""
        vis_data_dir = os.path.join(self.log_dir, 'vis_data')
        scalars_file = os.path.join(vis_data_dir, 'scalars.json')

        if not os.path.exists(scalars_file):
            print(f"vis_data文件不存在: {scalars_file}")
            return False

        try:
            with open(scalars_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            train_data_by_epoch = {}
            val_data_by_epoch = {}

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                data = json.loads(line)

                # 训练数据（包含epoch字段）
                if 'epoch' in data:
                    epoch = data['epoch']
                    if epoch not in train_data_by_epoch:
                        train_data_by_epoch[epoch] = []

                    train_data_by_epoch[epoch].append({
                        'lr': data.get('lr', 0),
                        'loss': data.get('loss', 0),
                        'top1_acc': data.get('top1_acc', 0),
                        'grad_norm': data.get('grad_norm', 0)
                    })

                # 验证数据（包含acc/top1字段但没有epoch）
                elif 'acc/top1' in data and 'step' in data:
                    epoch = data['step']
                    val_data_by_epoch[epoch] = {
                        'acc': data['acc/top1']
                    }

            # 处理数据：每个epoch取最后一条训练记录
            all_epochs = sorted(set(train_data_by_epoch.keys()) | set(val_data_by_epoch.keys()))

            for epoch in all_epochs:
                self.vis_epochs.append(epoch)

                # 训练数据（取每个epoch的最后一条）
                if epoch in train_data_by_epoch:
                    last_record = train_data_by_epoch[epoch][-1]
                    self.vis_train_acc.append(last_record['top1_acc'])
                    self.vis_train_loss.append(last_record['loss'])
                    self.vis_learning_rates.append(last_record['lr'])
                    self.vis_grad_norm.append(last_record['grad_norm'])
                else:
                    # 填充缺失数据
                    self.vis_train_acc.append(self.vis_train_acc[-1] if self.vis_train_acc else 0)
                    self.vis_train_loss.append(self.vis_train_loss[-1] if self.vis_train_loss else 0)
                    self.vis_learning_rates.append(self.vis_learning_rates[-1] if self.vis_learning_rates else 0)
                    self.vis_grad_norm.append(self.vis_grad_norm[-1] if self.vis_grad_norm else 0)

                # 验证数据
                if epoch in val_data_by_epoch:
                    self.vis_val_acc.append(val_data_by_epoch[epoch]['acc'])
                else:
                    self.vis_val_acc.append(None)

            self.vis_data_available = True
            print(f"成功解析vis_data文件: {scalars_file}")
            print(f"vis_data解析到 {len(self.vis_epochs)} 个epoch的数据")
            return True

        except Exception as e:
            print(f"解析vis_data文件失败: {e}")
            return False
    
    def plot_metrics(self) -> None:
        """绘制训练指标图"""
        if not self.epochs:
            print("没有数据可绘制")
            return
        
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'Training Metrics - {os.path.basename(self.log_path)}', fontsize=16, fontweight='bold')
        
        epochs = np.array(self.epochs)
        
        # 1. 准确率曲线
        ax1.plot(epochs, self.train_acc, 'b-', label='Training Accuracy', linewidth=2, marker='o', markersize=3)

        # 绘制验证准确率（跳过None值）
        val_epochs = []
        val_accs = []
        for i, acc in enumerate(self.val_acc):
            if acc is not None:
                val_epochs.append(epochs[i])
                val_accs.append(acc)

        if val_epochs:
            ax1.plot(val_epochs, val_accs, 'r-', label='Validation Accuracy', linewidth=2, marker='s', markersize=3)

        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.set_title('Accuracy Curve')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 🔥 改进1: 横坐标每5个epoch为1个单位
        ax1.xaxis.set_major_locator(ticker.MultipleLocator(5))
        ax1.xaxis.set_minor_locator(ticker.MultipleLocator(1))

        # 🔥 改进2: 精度曲线0.05为1个单位
        ax1.yaxis.set_major_locator(ticker.MultipleLocator(0.05))
        ax1.yaxis.set_minor_locator(ticker.MultipleLocator(0.01))
        ax1.set_ylim(0, 1.05)

        # 2. 损失曲线
        ax2.plot(epochs, self.train_loss, 'b-', label='Training Loss', linewidth=2, marker='o', markersize=3)

        # 🔥 改进3: 绘制验证损失（如果存在）
        val_loss_epochs = []
        val_losses = []
        for i, loss in enumerate(self.val_loss):
            if loss is not None:
                val_loss_epochs.append(epochs[i])
                val_losses.append(loss)

        if val_loss_epochs:
            ax2.plot(val_loss_epochs, val_losses, 'r-', label='Validation Loss', linewidth=2, marker='s', markersize=3)

        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.set_title('Loss Curve')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')  # 使用对数坐标

        # 🔥 改进1: 横坐标每5个epoch为1个单位
        ax2.xaxis.set_major_locator(ticker.MultipleLocator(5))
        ax2.xaxis.set_minor_locator(ticker.MultipleLocator(1))

        # 3. 学习率曲线
        ax3.plot(epochs, self.learning_rates, 'g-', label='Learning Rate', linewidth=2, marker='^', markersize=3)
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_title('Learning Rate Curve')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')  # 使用对数坐标

        # 🔥 改进1: 横坐标每5个epoch为1个单位
        ax3.xaxis.set_major_locator(ticker.MultipleLocator(5))
        ax3.xaxis.set_minor_locator(ticker.MultipleLocator(1))
        
        # 4. 训练-验证准确率对比（放大视图）
        if val_epochs:
            ax4.plot(epochs, self.train_acc, 'b-', label='Training Accuracy', linewidth=2, alpha=0.7)
            ax4.plot(val_epochs, val_accs, 'r-', label='Validation Accuracy', linewidth=2, alpha=0.7)

            # 计算准确率范围，设置合适的y轴范围
            all_accs = self.train_acc + val_accs
            min_acc = min(all_accs)
            max_acc = max(all_accs)
            margin = (max_acc - min_acc) * 0.1
            ax4.set_ylim(max(0, min_acc - margin), min(1, max_acc + margin))

            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('Accuracy')
            ax4.set_title('Accuracy Comparison (Zoomed)')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            # 🔥 改进1: 横坐标每5个epoch为1个单位
            ax4.xaxis.set_major_locator(ticker.MultipleLocator(5))
            ax4.xaxis.set_minor_locator(ticker.MultipleLocator(1))

            # 🔥 改进2: 精度曲线0.05为1个单位
            ax4.yaxis.set_major_locator(ticker.MultipleLocator(0.05))
            ax4.yaxis.set_minor_locator(ticker.MultipleLocator(0.01))
        else:
            ax4.text(0.5, 0.5, 'No Validation Data', ha='center', va='center', transform=ax4.transAxes, fontsize=14)
            ax4.set_title('Validation Data Missing')
        
        plt.tight_layout()
        
        # 保存图片
        save_path = os.path.join(self.log_dir, 'training_metrics.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training metrics plot saved to: {save_path}")

        plt.close()  # 关闭图形，不显示

    def plot_comprehensive_metrics(self) -> None:
        """绘制包含日志和vis_data的综合指标图"""
        if not self.epochs and not self.vis_epochs:
            print("没有数据可绘制")
            return

        # 创建更大的图形以容纳更多子图
        fig = plt.figure(figsize=(20, 15))

        # 创建3x3的子图布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

        fig.suptitle(f'Comprehensive Training Metrics - {os.path.basename(self.log_path)}',
                    fontsize=18, fontweight='bold')

        # 准备数据
        if self.epochs:
            log_epochs = np.array(self.epochs)
            log_val_epochs = []
            log_val_accs = []
            for i, acc in enumerate(self.val_acc):
                if acc is not None:
                    log_val_epochs.append(log_epochs[i])
                    log_val_accs.append(acc)

        if self.vis_data_available:
            vis_epochs = np.array(self.vis_epochs)
            vis_val_epochs = []
            vis_val_accs = []
            for i, acc in enumerate(self.vis_val_acc):
                if acc is not None:
                    vis_val_epochs.append(vis_epochs[i])
                    vis_val_accs.append(acc)

        # 1. 训练vs验证准确率 (0,0)
        ax1 = fig.add_subplot(gs[0, 0])
        # 优先使用vis_data（更详细），如果没有则使用log数据
        if self.vis_data_available:
            ax1.plot(vis_epochs, self.vis_train_acc, 'b-', label='Training Accuracy', linewidth=2, marker='o', markersize=2)
            if vis_val_epochs:
                ax1.plot(vis_val_epochs, vis_val_accs, 'r-', label='Validation Accuracy', linewidth=2, marker='s', markersize=2)
        elif self.epochs:
            ax1.plot(log_epochs, self.train_acc, 'b-', label='Training Accuracy', linewidth=2, marker='o', markersize=2)
            if log_val_epochs:
                ax1.plot(log_val_epochs, log_val_accs, 'r-', label='Validation Accuracy', linewidth=2, marker='s', markersize=2)

        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.set_title('Training vs Validation Accuracy')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_locator(ticker.MultipleLocator(5))
        ax1.yaxis.set_major_locator(ticker.MultipleLocator(0.05))

        # 2. 训练vs验证损失 (0,1)
        ax2 = fig.add_subplot(gs[0, 1])
        # 优先使用vis_data，如果没有则使用log数据
        if self.vis_data_available:
            ax2.plot(vis_epochs, self.vis_train_loss, 'b-', label='Training Loss', linewidth=2, marker='o', markersize=2)
            # vis_data通常没有验证损失，但如果有的话也显示
        elif self.epochs:
            ax2.plot(log_epochs, self.train_loss, 'b-', label='Training Loss', linewidth=2, marker='o', markersize=2)
            # 如果log中有验证损失，也显示
            val_loss_epochs = []
            val_losses = []
            for i, loss in enumerate(self.val_loss):
                if loss is not None:
                    val_loss_epochs.append(log_epochs[i])
                    val_losses.append(loss)
            if val_loss_epochs:
                ax2.plot(val_loss_epochs, val_losses, 'r-', label='Validation Loss', linewidth=2, marker='s', markersize=2)

        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.set_title('Training vs Validation Loss')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_yscale('log')
        ax2.xaxis.set_major_locator(ticker.MultipleLocator(5))

        # 3. 学习率变化 (0,2)
        ax3 = fig.add_subplot(gs[0, 2])
        # 优先使用vis_data，如果没有则使用log数据
        if self.vis_data_available:
            ax3.plot(vis_epochs, self.vis_learning_rates, 'g-', label='Learning Rate', linewidth=2, marker='^', markersize=2)
        elif self.epochs:
            ax3.plot(log_epochs, self.learning_rates, 'g-', label='Learning Rate', linewidth=2, marker='^', markersize=2)

        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_title('Learning Rate Schedule')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')
        ax3.xaxis.set_major_locator(ticker.MultipleLocator(5))

        # 4. 梯度范数 (1,0) - 仅vis_data有此数据
        ax4 = fig.add_subplot(gs[1, 0])
        if self.vis_data_available:
            ax4.plot(vis_epochs, self.vis_grad_norm, 'purple', label='Gradient Norm', linewidth=2, marker='d', markersize=2)
            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('Gradient Norm')
            ax4.set_title('Gradient Norm Evolution')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            ax4.xaxis.set_major_locator(ticker.MultipleLocator(5))
        else:
            ax4.text(0.5, 0.5, 'No vis_data available\nfor gradient norm', ha='center', va='center',
                    transform=ax4.transAxes, fontsize=12)
            ax4.set_title('Gradient Norm - No Data')

        # 5. 数据源对比 - 训练准确率 (1,1)
        ax5 = fig.add_subplot(gs[1, 1])
        if self.epochs and self.vis_data_available:
            ax5.plot(log_epochs, self.train_acc, 'b-', label='Log Data', linewidth=2, marker='o', markersize=2, alpha=0.8)
            ax5.plot(vis_epochs, self.vis_train_acc, 'r--', label='Vis Data', linewidth=2, marker='s', markersize=2, alpha=0.8)
            ax5.set_xlabel('Epoch')
            ax5.set_ylabel('Training Accuracy')
            ax5.set_title('Data Source Comparison (Train Acc)')
            ax5.legend()
            ax5.grid(True, alpha=0.3)
            ax5.xaxis.set_major_locator(ticker.MultipleLocator(5))
            ax5.yaxis.set_major_locator(ticker.MultipleLocator(0.05))
        else:
            ax5.text(0.5, 0.5, 'Need both log and vis_data\nfor comparison', ha='center', va='center',
                    transform=ax5.transAxes, fontsize=12)
            ax5.set_title('Data Source Comparison - No Data')

        # 6. 数据源对比 - 学习率 (1,2)
        ax6 = fig.add_subplot(gs[1, 2])
        if self.epochs and self.vis_data_available:
            ax6.plot(log_epochs, self.learning_rates, 'b-', label='Log Data', linewidth=2, marker='o', markersize=2, alpha=0.8)
            ax6.plot(vis_epochs, self.vis_learning_rates, 'r--', label='Vis Data', linewidth=2, marker='s', markersize=2, alpha=0.8)
            ax6.set_xlabel('Epoch')
            ax6.set_ylabel('Learning Rate')
            ax6.set_title('Data Source Comparison (Learning Rate)')
            ax6.legend()
            ax6.grid(True, alpha=0.3)
            ax6.set_yscale('log')
            ax6.xaxis.set_major_locator(ticker.MultipleLocator(5))
        else:
            ax6.text(0.5, 0.5, 'Need both log and vis_data\nfor comparison', ha='center', va='center',
                    transform=ax6.transAxes, fontsize=12)
            ax6.set_title('Data Source Comparison - No Data')

        # 7-9. 详细视图
        # 7. 准确率详细视图 (2,0)
        ax7 = fig.add_subplot(gs[2, 0])
        # 显示最详细的数据（优先vis_data）
        if self.vis_data_available:
            ax7.plot(vis_epochs, self.vis_train_acc, 'b-', label='Training', linewidth=2, marker='o', markersize=1.5)
            if vis_val_epochs:
                ax7.plot(vis_val_epochs, vis_val_accs, 'r-', label='Validation', linewidth=2, marker='s', markersize=1.5)
        elif self.epochs:
            ax7.plot(log_epochs, self.train_acc, 'b-', label='Training', linewidth=2, marker='o', markersize=1.5)
            if log_val_epochs:
                ax7.plot(log_val_epochs, log_val_accs, 'r-', label='Validation', linewidth=2, marker='s', markersize=1.5)

        ax7.set_xlabel('Epoch')
        ax7.set_ylabel('Accuracy')
        ax7.set_title('Accuracy Detail View')
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        ax7.xaxis.set_major_locator(ticker.MultipleLocator(5))
        ax7.yaxis.set_major_locator(ticker.MultipleLocator(0.05))

        # 8. 损失详细视图 (2,1)
        ax8 = fig.add_subplot(gs[2, 1])
        # 显示最详细的损失数据
        if self.vis_data_available:
            ax8.plot(vis_epochs, self.vis_train_loss, 'b-', label='Training Loss', linewidth=2, marker='o', markersize=1.5)
        elif self.epochs:
            ax8.plot(log_epochs, self.train_loss, 'b-', label='Training Loss', linewidth=2, marker='o', markersize=1.5)
            # 如果有验证损失也显示
            val_loss_epochs = []
            val_losses = []
            for i, loss in enumerate(self.val_loss):
                if loss is not None:
                    val_loss_epochs.append(log_epochs[i])
                    val_losses.append(loss)
            if val_loss_epochs:
                ax8.plot(val_loss_epochs, val_losses, 'r-', label='Validation Loss', linewidth=2, marker='s', markersize=1.5)

        ax8.set_xlabel('Epoch')
        ax8.set_ylabel('Loss')
        ax8.set_title('Loss Detail View')
        ax8.legend()
        ax8.grid(True, alpha=0.3)
        ax8.set_yscale('log')
        ax8.xaxis.set_major_locator(ticker.MultipleLocator(5))

        # 9. 学习率详细视图 (2,2)
        ax9 = fig.add_subplot(gs[2, 2])
        # 显示最详细的学习率数据
        if self.vis_data_available:
            ax9.plot(vis_epochs, self.vis_learning_rates, 'g-', label='Learning Rate', linewidth=2, marker='^', markersize=1.5)
        elif self.epochs:
            ax9.plot(log_epochs, self.learning_rates, 'g-', label='Learning Rate', linewidth=2, marker='^', markersize=1.5)

        ax9.set_xlabel('Epoch')
        ax9.set_ylabel('Learning Rate')
        ax9.set_title('Learning Rate Detail View')
        ax9.legend()
        ax9.grid(True, alpha=0.3)
        ax9.set_yscale('log')
        ax9.xaxis.set_major_locator(ticker.MultipleLocator(5))

        # 保存图片
        save_path = os.path.join(self.log_dir, 'comprehensive_metrics.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Comprehensive metrics plot saved to: {save_path}")

        plt.close()  # 关闭图形，不显示
    
    def plot_learning_rate(self) -> None:
        """单独绘制学习率曲线图"""
        if not self.epochs:
            print("没有数据可绘制")
            return
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))
        
        epochs = np.array(self.epochs)
        ax.plot(epochs, self.learning_rates, 'g-', label='Learning Rate', linewidth=2, marker='o', markersize=4)
        
        ax.set_xlabel('Epoch', fontsize=12)
        ax.set_ylabel('Learning Rate', fontsize=12)
        ax.set_title(f'Learning Rate Curve - {os.path.basename(self.log_path)}', fontsize=14, fontweight='bold')
        ax.legend(fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')

        # 🔥 改进1: 横坐标每5个epoch为1个单位
        ax.xaxis.set_major_locator(ticker.MultipleLocator(5))
        ax.xaxis.set_minor_locator(ticker.MultipleLocator(1))

        # 添加数值标注（每10个epoch标注一次）
        for i in range(0, len(epochs), max(1, len(epochs)//10)):
            ax.annotate(f'{self.learning_rates[i]:.2e}',
                       (epochs[i], self.learning_rates[i]),
                       textcoords="offset points",
                       xytext=(0,10),
                       ha='center', fontsize=8, alpha=0.7)

        plt.tight_layout()

        # 保存图片
        save_path = os.path.join(self.log_dir, 'learning_rate_curve.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Learning rate curve saved to: {save_path}")

        plt.close()  # 关闭图形，不显示
    
    def print_summary(self) -> None:
        """打印训练摘要"""
        if not self.epochs:
            return
        
        print("\n" + "="*50)
        print("Training Summary")
        print("="*50)
        print(f"Total epochs: {max(self.epochs)}")
        print(f"Final training accuracy: {self.train_acc[-1]:.4f}")
        print(f"Final training loss: {self.train_loss[-1]:.6f}")
        print(f"Final learning rate: {self.learning_rates[-1]:.2e}")

        # 验证集最佳结果
        val_accs_clean = [acc for acc in self.val_acc if acc is not None]
        if val_accs_clean:
            best_val_acc = max(val_accs_clean)
            best_val_epoch = None
            for i, acc in enumerate(self.val_acc):
                if acc == best_val_acc:
                    best_val_epoch = self.epochs[i]
                    break
            print(f"Best validation accuracy: {best_val_acc:.4f} (Epoch {best_val_epoch})")
            print(f"Final validation accuracy: {val_accs_clean[-1]:.4f}")


def main():
    parser = argparse.ArgumentParser(description='Plot MMAction2 training log metrics')
    parser.add_argument('log_path', help='Path to training log file')
    parser.add_argument('--lr-only', action='store_true', help='Only plot learning rate curve')
    parser.add_argument('--comprehensive', action='store_true', help='Plot comprehensive metrics including vis_data')

    args = parser.parse_args()

    if not os.path.exists(args.log_path):
        print(f"Error: Log file does not exist: {args.log_path}")
        sys.exit(1)

    # 解析日志
    log_parser = TrainingLogParser(args.log_path)
    if not log_parser.parse_log():
        sys.exit(1)

    # 🔥 新增：解析vis_data（如果存在）
    log_parser.parse_vis_data()

    # 打印摘要
    log_parser.print_summary()

    # 绘制图表
    if args.lr_only:
        log_parser.plot_learning_rate()
    elif args.comprehensive:
        # 🔥 新增：绘制综合指标图
        log_parser.plot_comprehensive_metrics()
    else:
        log_parser.plot_metrics()
        log_parser.plot_learning_rate()


if __name__ == "__main__":
    main()
