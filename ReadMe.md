# 蒙板工具使用说明

## 1. 项目概述

本工具是一个基于Web的本地应用程序，旨在通过交互式操作，将正常的仰卧起坐样本图像（`0_normal`）转换为模拟的作弊样本（`Fake_AI`）。它通过让用户在正常样本上标注关键点，然后将一个代表作弊工具（如绳索、衣物）的透明蒙版图像叠加到这些关键点之间，来生成逼真的作弊数据。

项目包含两种核心模式：
- **模式一：创建蒙版**：从任意源图片中，通过勾勒多边形来创建透明的作弊工具蒙版。
- **模式二：生成数据**：使用已创建的蒙版，在正常样本上进行标注并生成作弊样本。

---

## 2. 环境配置

在运行此工具前，请确保您已配置好Python环境并安装了所有必需的依赖库。

1.  **安装依赖**:
    打开终端，进入项目根目录，然后运行以下命令来安装所有必需的库：
    ```bash
    pip install -r requirements.txt
    ```
    *`requirements.txt` 文件内容如下:*
    ```
    Flask
    numpy
    opencv-python
    ```

---

## 3. 运行工具

完成环境配置后，通过以下命令启动Web应用程序：

```bash
python app.py
```

终端将显示应用正在运行，并提供一个本地网址，通常是 `http://127.0.0.1:5001`。请在您的网页浏览器中打开此地址。

---

## 4. 核心功能与使用流程

### 模式一：创建蒙版

此模式用于从常规图片中提取一个透明背景的作弊工具（例如，提取图中的一根绳子）。

1.  **选择源图片**：在左侧“文件浏览器”中，浏览并点击您想要用作素材的图片（默认从 `pose_rgb/Fake_tool` 加载，可在“路径配置”中更改）。图片将加载到主画布上。
2.  **勾勒工具轮廓**：在画布上，沿着您想要提取的物体的边缘，通过鼠标左键单击来放置多个点，形成一个封闭的多边形。
    - **操作提示**:
        - **缩放**: 按住 `Ctrl` 键并滚动鼠标滚轮，可以以鼠标指针为中心进行缩放。
        - **平移**: 在画布上按住鼠标左键并拖动，可以平移视图。
        - **撤销**: 按 `Ctrl + Z` 可以撤销上一个标注的点。
3.  **保存蒙版**：完成勾勒后，按 Enter 键闭合多边形，然后点击左侧面板的“保存蒙版”按钮。系统将弹出“蒙版创建成功！”的提示。此时，一个背景透明的PNG蒙版文件已被保存到 `pose_rgb/Fake_tool_masks` 目录下（或“路径配置”中设置的目录）。

### 模式二：生成数据

此模式是核心功能，用于将蒙版应用到正常样本上以生成作弊数据。

1.  **切换模式**：在控制面板顶部，点击“模式二: 生成数据”按钮。
2.  **加载列表**：程序会自动加载 `pose_rgb/Fake_tool_masks` 目录下的所有可用蒙版和 `pose_rgb/0_normal` 目录下的所有待处理样本（两者均可在“路径配置”中更改），并显示在左侧的两个列表中。
3.  **选择蒙版**：在“工具蒙版列表”中点击您想使用的蒙版。点击后，您会在主操作区下方看到该蒙版的预览图。
4.  **选择样本**：在“待处理样本”列表中点击一个样本文件夹，或者直接使用默认加载的第一个样本。对应的第一张图片会自动加载到画布上。
5.  **标注关键点**：在画布上，依次用鼠标左键点击作弊工具的起始和结束位置（例如，两个手腕的位置）。画布上会出现两个红点。
6.  **生成并预览**：完成两点标注后，点击“处理下一个”按钮。系统会弹出一个预览窗口，向您展示作弊工具蒙版被精确地拉伸并叠加在您标注的两点之间的效果图。
7.  **确认或拒绝**：
    - **确认并继续**：如果对结果满意，点击此按钮。系统将保存这张生成的图片，并自动加载当前样本的下一张图片（或下一个样本的第一张图片），让您继续标注。
    - **拒绝并重做**：如果对结果不满意，点击此按钮。系统将丢弃这次生成的结果，并允许您对当前图片重新进行标注。

---

## 5. 目录结构说明

-   `pose_rgb/0_normal/`: **输入目录**。存放原始的、正常的仰卧起坐样本文件夹。每个子文件夹代表一个样本，包含3张 `.jpg` 图片以及 `.avi` 和 `.pkl` 元数据文件。
-   `pose_rgb/Fake_tool/`: **蒙版素材目录**。存放用于制作蒙版的原始图片。
-   `pose_rgb/Fake_tool_masks/`: **蒙版输出目录**。所有在“模式一”中创建的透明背景PNG蒙版都保存在这里。
-   `pose_rgb/Fake_AI/`: **最终输出目录**。所有在“模式二”中成功生成的作弊样本都保存在这里。程序会自动创建与原始样本同名的子文件夹，并保留其3张图的结构，同时拷贝原始的 `.avi` 和 `.pkl` 文件。
-   `app.py`: 主程序文件。
-   `templates/index.html`: 前端页面结构。
-   `static/`: 存放前端的 `style.css` 和 `script.js` 文件。

---

## V2.0 版本重大更新

### 🚀 新功能特性

**V2.0版本**在V1.0基础上进行了全面重构，解决了坐标精度问题并增加了强大的实时预览和参数调节功能。

#### 1. 精确坐标系统
- ✅ **问题解决**: 修复了鼠标点击位置与红点显示位置的偏差问题
- ✅ **技术升级**: 使用`DOMMatrix`确保高DPI显示器下的精确坐标转换
- ✅ **用户体验**: 点击位置与显示位置完全一致，支持复杂变换后的精确操作

#### 2. 可视化参数控制面板
- 🎛️ **边缘融合强度**: 0.0-1.0范围内精确调节
- 🎨 **泊松融合模式**: NORMAL_CLONE/MIXED_CLONE模式选择
- 🌈 **颜色调整**: 蒙版透明度、亮度、对比度实时调节
- ⚖️ **融合权重**: 无痕融合权重动态控制
- 💾 **参数预设**: 支持保存和加载常用参数组合

#### 3. 主画布实时预览
- 🖼️ **直观预览**: 直接在主画布上显示最终效果，移除了无效的小预览窗口
- ⚡ **实时响应**: 参数调整后300ms内更新预览效果
- 🔄 **预览切换**: 支持在预览图和原图间快速切换
- 🎯 **真实算法**: 使用与最终生成完全相同的后端处理算法

### 🎮 V2.0 操作指南

#### 增强的交互控制
- **精确缩放**: `Ctrl + 鼠标滚轮` - 以鼠标指针为中心进行缩放
- **精确平移**: `Ctrl + 拖拽` - 拖动画布进行平移
- **撤销操作**: `Ctrl + Z` - 撤销最后一个标注点
- **面板切换**: `Ctrl + P` - 展开/折叠参数面板
- **参数重置**: `Ctrl + R` - 重置所有参数到默认值

#### 简化的工作流程

**V1.0 流程（已淘汰）**:
```
选择图片 → 点击两点 → 小预览窗口 → 应用到全图 → 确认结果
```

**V2.0 新流程**:
```
选择图片 → 点击两点 → 主画布实时预览 → 调整参数观察效果 → 满意后点击下一个
```

#### 参数面板详细说明

**融合参数组**:
- `边缘融合强度`: 控制蒙版边缘的融合程度（0.0=完全分离，1.0=完全融合）
- `泊松融合模式`: 选择融合算法（NORMAL_CLONE保持细节，MIXED_CLONE更自然）
- `无痕融合权重`: 调节Poisson融合与Alpha混合的比例

**颜色调整组**:
- `蒙版透明度`: 控制整体蒙版的透明程度
- `亮度调整`: 调节蒙版区域的亮度（-50到+50）
- `对比度调整`: 调节蒙版区域的对比度（0.5到2.0）

**预览设置组**:
- `实时预览`: 开启/关闭参数调整时的实时预览
- `预览尺寸`: 选择预览区域的尺寸（小/中/大）

### 🏗️ V2.0 技术架构

#### 模块化JavaScript架构
```
static/src/
├── managers/
│   ├── CoordinateManager.js     # 精确坐标转换管理
│   └── ModeManager.js           # 模式切换和状态管理
├── components/
│   ├── ParameterPanel.js        # 参数控制面板组件
│   └── MainCanvasPreview.js     # 主画布实时预览引擎
├── utils/
│   ├── mathUtils.js             # 数学计算工具函数
│   └── imageUtils.js            # 图像处理工具函数
└── app.js                       # 主应用程序入口
```

#### 响应式UI设计
- **三栏布局**: 左侧控制面板 + 中央画布 + 右侧参数面板
- **可折叠面板**: 参数面板支持展开/折叠，优化工作空间利用
- **响应式适配**: 自动适配桌面和移动设备的不同屏幕尺寸

#### 后端API扩展
- **动态参数支持**: `/api/generate_sample` 支持所有融合参数的动态传递
- **实时预览API**: `/api/realtime_preview` 返回base64编码的预览图像
- **参数验证**: 完整的参数范围验证和错误处理机制

### 📊 版本对比

| 特性 | V1.0 | V2.0 |
|------|------|------|
| 坐标精度 | 存在偏差 | ✅ 精确无误 |
| 参数调节 | 固化参数 | ✅ 6大类参数可调 |
| 预览功能 | 小窗口预览 | ✅ 主画布实时预览 |
| 用户界面 | 基础布局 | ✅ 响应式三栏布局 |
| 交互控制 | 基本操作 | ✅ 增强交互+快捷键 |
| 代码架构 | 单体结构 | ✅ 模块化组件 |
| 性能优化 | 无 | ✅ 防抖+缓存优化 |

### 🔧 V2.0 安装和运行

**环境要求**:
```bash
# 推荐使用torch虚拟环境
conda activate torch
pip install -r requirements.txt
```

**启动应用**:
```bash
python app.py
```

**访问地址**: http://localhost:5001

### 💡 V2.0 使用技巧

1. **参数调节建议**:
   - 从默认参数开始，逐步微调
   - 边缘融合强度建议在0.4-0.8范围内
   - 亮度对比度调整要适度，避免过度处理

2. **工作效率提升**:
   - 使用参数预设功能保存常用配置
   - 利用实时预览功能快速验证效果
   - 善用快捷键提高操作速度

3. **质量控制**:
   - 在预览模式下仔细检查边缘融合效果
   - 确保蒙版颜色与原图协调
   - 利用预览切换功能对比原图和效果图




