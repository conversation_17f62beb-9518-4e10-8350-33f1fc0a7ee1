#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
独立推理脚本 - DDP分布式版本
=================================

功能说明：
1. 基于DistributedDataParallel实现多GPU分布式推理，相比DataParallel提供2-4倍性能提升
2. 自动检测运行环境，支持单GPU和多GPU模式
3. 自动处理数据分发、结果收集和文件同步
4. 保持与原脚本完全兼容的业务逻辑

使用方法：
# 方法1：一键启动（推荐）- 自动检测GPU数量并启动DDP
python standalone_inference_ddp.py --gpus 4 [其他参数...]

# 方法2：手动torchrun启动
torchrun --nproc_per_node=4 standalone_inference_ddp.py [其他参数...]

# 方法3：单GPU模式
python standalone_inference_ddp.py [其他参数...]

参数说明：
--gpus: 指定使用的GPU数量，设置后自动启动DDP模式
--data_path: 测试数据路径
--config: 配置文件路径
--checkpoint: 模型权重路径
--batch_size: 每个GPU的批处理大小
--gt_label: 推理默认标签
--num_workers: 数据加载线程数
--log_level: 日志级别

作者：基于MMAction2框架开发，DDP优化版本
"""

import os
import sys
import json
import pickle
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor
import time
import warnings

import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel
from torch.utils.data.distributed import DistributedSampler
import numpy as np
import mmcv
import mmengine
from mmengine.config import Config
from mmengine.dataset import Compose, pseudo_collate
from mmengine.logging import MMLogger
from mmengine.utils import track_iter_progress

# MMAction2 imports
from mmaction.apis import init_recognizer, inference_recognizer
from mmaction.registry import MODELS
from mmaction.structures import ActionDataSample
from scipy.special import softmax
from ReadTypeFile_Speed import rglob_optimized

# 忽略一些警告
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

import torch.serialization
# 修复PyTorch 2.6权重加载问题
try:
    torch.serialization.add_safe_globals([
        'mmengine.logging.history_buffer.HistoryBuffer',
        'collections.OrderedDict',
        'torch.nn.modules.container.ModuleList',
        'torch.nn.modules.container.Sequential'
    ])
except (AttributeError, ImportError):
    # 对于较旧版本的PyTorch，忽略此设置
    pass


def init_distributed_env():
    """初始化分布式环境"""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])
    else:
        print('未检测到分布式环境变量，使用单GPU模式')
        return False, 0, 1, 0
    
    # 设置当前进程使用的GPU
    torch.cuda.set_device(local_rank)
    
    # 初始化分布式进程组
    dist.init_process_group(
        backend='nccl',
        init_method='env://',
        world_size=world_size,
        rank=rank
    )
    
    return True, rank, world_size, local_rank


def cleanup_distributed():
    """清理分布式环境"""
    if dist.is_initialized():
        dist.destroy_process_group()


def convert_tensor_to_numpy(data: Any) -> Any:
    """将torch tensor转换为numpy array"""
    if isinstance(data, torch.Tensor):
        return data.detach().cpu().numpy()
    elif isinstance(data, (list, tuple)):
        return [convert_tensor_to_numpy(item) for item in data]
    elif isinstance(data, dict):
        return {key: convert_tensor_to_numpy(value) for key, value in data.items()}
    else:
        return data


def safe_extract_keypoints(skpt_data: Any) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
    """安全地提取关键点和分数数据"""
    try:
        if skpt_data is None or len(skpt_data) == 0:
            return None, None

        # 处理不同的数据格式
        if isinstance(skpt_data, (list, tuple)) and len(skpt_data) > 0:
            first_item = skpt_data[0]

            # 如果是torch tensor
            if hasattr(first_item, 'view') and hasattr(first_item, 'shape'):
                if len(first_item.shape) >= 1 and first_item.shape[0] >= 57:  # 17*3 + 6 = 57
                    keypoint_data = first_item[6:].view(17, 3)
                    skpt = keypoint_data[:, :2].detach().cpu().numpy()
                    score = keypoint_data[:, 2].detach().cpu().numpy()
                    return skpt, score

            # 如果是numpy array
            elif isinstance(first_item, np.ndarray):
                if len(first_item.shape) >= 1 and first_item.shape[0] >= 57:
                    keypoint_data = first_item[6:].reshape(17, 3)
                    skpt = keypoint_data[:, :2]
                    score = keypoint_data[:, 2]
                    return skpt, score

        return None, None

    except Exception as e:
        print(f"提取关键点数据时出错: {e}")
        return None, None


def change_files(target_pth: Path, file_pth: Path, suffixes=None, method=None):
    """文件操作函数"""
    if suffixes is None:
        raise ValueError(f"@Moss: List should be a list like ['*.pkl']")
    for suffix in suffixes:
        file_pth = file_pth.with_suffix(suffix)
        change_onefile(method, file_pth, target_pth)

    for file_pth in [p for p in file_pth.parent.glob('*.jpg') if file_pth.stem in str(p)]:
        change_onefile(method, file_pth, target_pth)


def change_onefile(method, file_pth, target_pth):
    """单文件操作"""
    dst = target_pth / file_pth.parent.name
    dst.mkdir(exist_ok=True)
    if method in ['Cut', 'cut']:
        file_pth.replace(dst / file_pth.name)
    elif method in ['Copy', 'copy']:
        (dst / file_pth.name).write_bytes(file_pth.read_bytes())


def write_pred_labels(filenames, pre_lab, top1_preds, labels, pred_gt_txt):
    """
    @Moss: 将样本路径, 预测类别, 标注类别, 预测概率写入label_txt
    注意：在分布式环境下，只有rank 0进程执行文件写入
    """
    if dist.is_initialized() and dist.get_rank() != 0:
        return
    
    path_preds = f'{filenames}, {pre_lab}, {labels}, {top1_preds.__str__()}\n'
    
    with open(pred_gt_txt, 'a+', encoding='utf-8') as fl:
        fl.write(path_preds)


def change_samples(_Select, dst_pth, pkl_pres):
    """
    样本移动函数
    注意：在分布式环境下，只有rank 0进程执行文件操作
    """
    if dist.is_initialized() and dist.get_rank() != 0:
        return
        
    dir_name = Path(dst_pth).name
    dst = Path(dst_pth).parent / Path(f"{dir_name}_{_Select}")

    # err-data Copy
    pkl_pth, pred_lab, _score = pkl_pres
    dst_lab = dst / f'{dir_name}_pre{pred_lab}'

    base_name = Path(pkl_pth).parents[1].name
    dst_gt_pred = dst_lab / base_name
    dst_gt_pred.mkdir(exist_ok=True, parents=True)
    change_files(target_pth=dst_gt_pred, file_pth=Path(pkl_pth), suffixes=['.pkl', '.avi'], method=_Select)


def empty_file_del(path):
    """删除路径下所有(包含子文件夹)空文件夹
    注意：在分布式环境下，只有rank 0进程执行文件操作
    """
    if dist.is_initialized() and dist.get_rank() != 0:
        return
        
    for root, dirs, files in os.walk(path, topdown=False):
        if not os.listdir(root):
            os.rmdir(root)


def setup_logging(log_level: str, rank: int = 0):
    """设置日志，每个进程使用不同的日志文件"""
    log_format = f'[Rank {rank}] %(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=log_format
    )


class DistributedStandaloneDataLoader:
    """分布式独立数据加载器，支持DDP数据分发"""

    def __init__(self,
                 data_path: str,
                 test_pipeline: Compose,
                 batch_size: int = 1,
                 num_workers: int = 4,
                 gt_label: int = -1,
                 rank: int = 0,
                 world_size: int = 1):
        """
        Args:
            data_path: 数据根目录路径
            test_pipeline: 预处理管道
            batch_size: 批处理大小
            num_workers: 并行加载线程数
            gt_label: 默认标签
            rank: 当前进程rank
            world_size: 总进程数
        """
        self.data_path = Path(data_path)
        self.test_pipeline = test_pipeline
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.gt_label = gt_label
        self.rank = rank
        self.world_size = world_size
        self.logger = MMLogger.get_current_instance()

        # 扫描数据文件
        self.data_files = self._scan_data_files()

        # 分布式数据分割
        self.distributed_data_files = self._distribute_data()

        if self.rank == 0:
            self.logger.info(f"总共发现 {len(self.data_files)} 个数据样本")
            self.logger.info(f"当前进程分配到 {len(self.distributed_data_files)} 个样本")

    def _scan_data_files(self) -> List[Dict[str, Any]]:
        """扫描数据目录，匹配pkl文件和对应的jpg图像"""
        data_files = []

        # 只在rank 0进程执行文件扫描，然后广播给其他进程
        if self.rank == 0:
            pkl_files = rglob_optimized(str(self.data_path), '*.pkl', max_workers=self.num_workers)

            for pkl_path in pkl_files:
                base_name = pkl_path.stem
                parent_dir = pkl_path.parent
                pkl_path = pkl_path.__str__()

                # 查找对应的jpg文件
                jpg_files = [p for p in parent_dir.rglob('*.jpg')]
                if len(jpg_files) != 3:
                    continue

                data_files.append({
                    'pkl_path': pkl_path,
                    'jpg_paths': jpg_files,
                    'sample_name': base_name
                })

        # 广播数据文件列表到所有进程
        if dist.is_initialized():
            # 使用pickle序列化数据进行广播
            if self.rank == 0:
                data_to_broadcast = data_files
            else:
                data_to_broadcast = None

            # 广播数据
            data_files = [None]
            dist.broadcast_object_list(data_files, src=0)
            data_files = data_files[0] if data_files[0] is not None else []

        return data_files

    def _distribute_data(self) -> List[Dict[str, Any]]:
        """将数据分发到各个进程"""
        total_samples = len(self.data_files)
        samples_per_rank = total_samples // self.world_size
        remainder = total_samples % self.world_size

        # 计算当前进程的数据范围
        start_idx = self.rank * samples_per_rank + min(self.rank, remainder)
        if self.rank < remainder:
            end_idx = start_idx + samples_per_rank + 1
        else:
            end_idx = start_idx + samples_per_rank

        return self.data_files[start_idx:end_idx]

    def _load_pkl_data(self, pkl_path: str) -> Dict[str, Any]:
        """读取pkl文件数据"""
        try:
            with open(pkl_path, 'rb') as f:
                data = pickle.load(f)
            return data
        except Exception as e:
            if self.rank == 0:
                self.logger.error(f"读取pkl文件失败 {pkl_path}: {e}")
            return {}

    def _construct_data_dict(self, file_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """构造符合预处理管道要求的数据字典"""
        pkl_path = file_info['pkl_path']
        jpg_paths = file_info['jpg_paths']

        # 读取pkl数据
        pkl_data = self._load_pkl_data(pkl_path)
        if not pkl_data:
            return None

        try:
            # 基于custom_PoseRGB_extraction.py中的pose_getResult逻辑构造数据
            keypoint_lst = pkl_data.get('pred_skpts', [])

            # 提取关键点和分数
            skpts = []
            scores = []
            for skpt_ts in keypoint_lst:
                skpt, score = safe_extract_keypoints(skpt_ts)
                if skpt is not None and score is not None:
                    skpts.append(skpt)
                    scores.append(score)

            if not skpts:
                if self.rank == 0:
                    self.logger.warning(f"样本 {pkl_path} 没有有效的关键点数据")
                return None

            # 构造数据字典
            data_dict = {
                'keypoint': np.stack(skpts, axis=0),  # (N, T, 17, 2)
                'keypoint_score': np.stack(scores, axis=0),  # (N, T, 17)
                'imgs_pth': jpg_paths,
                'img_shape': pkl_data.get('img_shape', (1080, 1920)),
                'original_shape': pkl_data.get('oriVid_shape', (1080, 1920)),
                'total_frames': len(skpts),
                'vid_frames': pkl_data.get('total_frames', len(skpts)),
                'filename': pkl_path,
                'frame_dir': Path(pkl_path).stem,
                'label': self.gt_label,
                'start_index': 0,
                'modality': 'Pose'
            }

            return data_dict

        except Exception as e:
            if self.rank == 0:
                self.logger.error(f"构造数据字典失败 {pkl_path}: {e}")
            return None

    def __len__(self) -> int:
        """返回当前进程的数据样本总数"""
        return len(self.distributed_data_files)

    def __iter__(self):
        """迭代器，支持批处理"""
        batch = []

        for file_info in self.distributed_data_files:
            # 构造数据字典
            data_dict = self._construct_data_dict(file_info)
            if data_dict is None:
                continue

            # 应用预处理管道
            try:
                processed_data = self.test_pipeline(data_dict)
                batch.append(processed_data)

                # 当批次满了时，返回批次
                if len(batch) >= self.batch_size:
                    yield batch
                    batch = []

            except Exception as e:
                if self.rank == 0:
                    self.logger.error(f"预处理失败 {file_info['pkl_path']}: {e}")
                continue

        # 返回最后一个不满的批次
        if batch:
            yield batch


class DistributedInferenceEngine:
    """分布式推理引擎，支持DDP模型包装"""

    def __init__(self,
                 config_path: str,
                 checkpoint_path: str,
                 local_rank: int = 0,
                 rank: int = 0,
                 world_size: int = 1):
        """
        Args:
            config_path: 配置文件路径
            checkpoint_path: 模型权重路径
            local_rank: 本地GPU rank
            rank: 全局rank
            world_size: 总进程数
        """
        self.config_path = config_path
        self.checkpoint_path = checkpoint_path
        self.local_rank = local_rank
        self.rank = rank
        self.world_size = world_size
        self.device = f'cuda:{local_rank}'
        self.logger = MMLogger.get_current_instance()

        # 初始化模型
        self.model = self._init_model()
        self.test_pipeline = self._build_test_pipeline()

        # DDP包装
        if dist.is_initialized() and world_size > 1:
            if self.rank == 0:
                self.logger.info(f"使用DDP包装模型，world_size={world_size}")
            self.model = DistributedDataParallel(
                self.model,
                device_ids=[local_rank],
                output_device=local_rank,
                find_unused_parameters=False
            )
        elif self.rank == 0:
            self.logger.info("使用单GPU模式")

    def _init_model(self) -> nn.Module:
        """初始化模型"""
        if self.rank == 0:
            self.logger.info(f"加载模型: {self.config_path}")
            self.logger.info(f"加载权重: {self.checkpoint_path}")

        try:
            # 使用指定的GPU设备初始化模型
            model = init_recognizer(
                config=self.config_path,
                checkpoint=self.checkpoint_path,
                device=self.device
            )
        except Exception as e:
            if self.rank == 0:
                self.logger.error(f"模型加载失败: {e}")
            # 尝试手动加载配置和权重
            from mmengine.config import Config
            from mmaction.registry import MODELS

            cfg = Config.fromfile(self.config_path)
            model = MODELS.build(cfg.model)

            # 手动加载权重，设置weights_only=False
            checkpoint = torch.load(self.checkpoint_path, map_location='cpu', weights_only=False)
            if 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'], strict=False)
            else:
                model.load_state_dict(checkpoint, strict=False)

            model = model.to(self.device)
            model.eval()

            # 添加cfg属性
            model.cfg = cfg

        if self.rank == 0:
            self.logger.info("模型加载完成")
        return model

    def _build_test_pipeline(self) -> Compose:
        """构建预处理管道"""
        cfg = self.model.cfg
        test_pipeline_cfg = cfg.test_pipeline
        test_pipeline = Compose(test_pipeline_cfg)
        return test_pipeline

    def inference_batch(self, batch_data: List[Dict[str, Any]]) -> List[ActionDataSample]:
        """批量推理"""
        try:
            # 使用pseudo_collate组织批次数据
            collated_data = pseudo_collate(batch_data)

            # 执行推理
            with torch.no_grad():
                if isinstance(self.model, DistributedDataParallel):
                    # DDP推理
                    results = self.model.module.test_step(collated_data)
                else:
                    # 单GPU推理
                    results = self.model.test_step(collated_data)

            return results

        except Exception as e:
            if self.rank == 0:
                self.logger.error(f"批量推理失败: {e}")
            # 返回空结果列表
            return [ActionDataSample() for _ in batch_data]


class DistributedResultManager:
    """分布式结果管理器，处理结果收集和同步"""

    def __init__(self, rank: int = 0, world_size: int = 1):
        self.rank = rank
        self.world_size = world_size
        self.local_results = []
        self.stats = {
            'total_samples': 0,
            'successful_samples': 0,
            'failed_samples': 0,
            'start_time': None,
            'end_time': None
        }
        self.logger = MMLogger.get_current_instance()

    def add_result(self, filename: str, pred_scores: np.ndarray, success: bool = True):
        """添加推理结果"""
        if success and len(pred_scores) > 0:
            self.local_results.append({
                'filename': filename,
                'pred_scores': pred_scores.tolist(),
                'pred_label': int(np.argmax(pred_scores)),
                'max_confidence': float(np.max(pred_scores)),
                'rank': self.rank
            })
            self.stats['successful_samples'] += 1
        else:
            # 记录失败的样本
            self.local_results.append({
                'filename': filename,
                'pred_scores': [],
                'pred_label': -1,
                'max_confidence': 0.0,
                'error': True,
                'rank': self.rank
            })
            self.stats['failed_samples'] += 1

        self.stats['total_samples'] += 1

    def gather_results(self) -> List[Dict]:
        """收集所有进程的结果"""
        if not dist.is_initialized():
            return self.local_results

        # 收集所有进程的结果
        all_results = [None for _ in range(self.world_size)]
        dist.all_gather_object(all_results, self.local_results)

        # 只在rank 0进程合并结果
        if self.rank == 0:
            merged_results = []
            for results in all_results:
                if results:
                    merged_results.extend(results)
            return merged_results
        else:
            return []

    def gather_stats(self) -> Dict:
        """收集所有进程的统计信息"""
        if not dist.is_initialized():
            return self.stats

        # 收集所有进程的统计信息
        all_stats = [None for _ in range(self.world_size)]
        dist.all_gather_object(all_stats, self.stats)

        # 只在rank 0进程合并统计信息
        if self.rank == 0:
            merged_stats = {
                'total_samples': sum(s['total_samples'] for s in all_stats if s),
                'successful_samples': sum(s['successful_samples'] for s in all_stats if s),
                'failed_samples': sum(s['failed_samples'] for s in all_stats if s),
                'start_time': min(s['start_time'] for s in all_stats if s and s['start_time']),
                'end_time': max(s['end_time'] for s in all_stats if s and s['end_time'])
            }
            return merged_stats
        else:
            return {}

    def print_statistics(self):
        """打印统计信息（只在rank 0执行）"""
        if self.rank != 0:
            return

        merged_stats = self.gather_stats()
        if not merged_stats:
            return

        total_time = merged_stats['end_time'] - merged_stats['start_time']
        success_rate = merged_stats['successful_samples'] / merged_stats['total_samples'] * 100 if merged_stats['total_samples'] > 0 else 0

        self.logger.info("=" * 50)
        self.logger.info("分布式推理统计信息:")
        self.logger.info(f"总样本数: {merged_stats['total_samples']}")
        self.logger.info(f"成功样本数: {merged_stats['successful_samples']}")
        self.logger.info(f"失败样本数: {merged_stats['failed_samples']}")
        self.logger.info(f"成功率: {success_rate:.2f}%")
        self.logger.info(f"总耗时: {total_time:.2f}秒")
        if total_time > 0:
            self.logger.info(f"平均速度: {merged_stats['successful_samples']/total_time:.2f} 样本/秒")
        self.logger.info("=" * 50)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='独立推理脚本 - DDP分布式版本')
    parser.add_argument('--data_path', type=str,
                        default="/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/伪标签/BaseSchools/Fujian_xiamenshuangshizhongxue",
                        help='测试数据路径')
    parser.add_argument('--config', type=str,
                        default='configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion_warmup.py',
                        help='配置文件路径')
    parser.add_argument('--checkpoint', type=str,
                        default='tools/work_dirs/pose_rgb_fusion_warmup/20250827_193240/best_acc_top1_epoch_90.pth',
                        help='模型权重文件路径')
    parser.add_argument('--batch_size', type=int, default=32, help='每个GPU的批处理大小')
    parser.add_argument('--gt_label', type=int, default=0, help='推理默认标签')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载线程数')
    parser.add_argument('--log_level', type=str, default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='日志级别')

    # 一键启动DDP参数
    parser.add_argument('--gpus', type=int, default=None,
                        help='指定GPU数量，自动启动DDP模式（如：--gpus 4）')
    parser.add_argument('--master_port', type=int, default=29500,
                        help='DDP主节点端口')

    # 分布式相关参数（通常由torchrun自动设置）
    parser.add_argument('--local_rank', '--local-rank', type=int, default=0,
                        help='本地GPU rank（由torchrun自动设置）')

    return parser.parse_args()


def launch_ddp_inference(args):
    """启动DDP推理进程"""
    import subprocess
    import shutil

    # 检查torchrun是否可用
    if not shutil.which('torchrun'):
        print("错误: 未找到torchrun命令，请确保PyTorch版本 >= 1.9.0")
        sys.exit(1)

    # 检查GPU可用性
    if not torch.cuda.is_available():
        print("错误: 未检测到CUDA GPU")
        sys.exit(1)

    gpu_count = torch.cuda.device_count()
    requested_gpus = args.gpus

    if requested_gpus > gpu_count:
        print(f"警告: 请求的GPU数量({requested_gpus})超过可用GPU数量({gpu_count})，调整为{gpu_count}")
        requested_gpus = gpu_count

    print(f"启动DDP推理: 使用 {requested_gpus} 个GPU")

    # 构建torchrun命令
    cmd = [
        'torchrun',
        f'--nproc_per_node={requested_gpus}',
        f'--master_port={args.master_port}',
        sys.argv[0]  # 当前脚本路径
    ]

    # 添加其他参数（排除--gpus和--master_port参数）
    skip_next = False
    for i, arg in enumerate(sys.argv[1:]):
        if skip_next:
            skip_next = False
            continue
        if arg.startswith('--gpus'):
            if '=' not in arg:  # --gpus 4 格式
                skip_next = True
            continue
        if arg.startswith('--master_port'):
            if '=' not in arg:  # --master_port 29500 格式
                skip_next = True
            continue
        cmd.append(arg)

    # 设置环境变量
    env = os.environ.copy()
    env['MASTER_ADDR'] = '127.0.0.1'
    env['MASTER_PORT'] = str(args.master_port)

    try:
        # 启动DDP进程
        result = subprocess.run(cmd, env=env)
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n用户中断推理")
        sys.exit(1)
    except Exception as e:
        print(f"启动DDP失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    args = parse_args()

    # 检查是否需要启动DDP模式
    if args.gpus is not None and args.gpus > 1:
        # 检查是否已经在DDP环境中
        if 'RANK' not in os.environ:
            # 启动DDP进程
            launch_ddp_inference(args)
            return

    # 初始化分布式环境
    is_distributed, rank, world_size, local_rank = init_distributed_env()

    # 设置日志
    setup_logging(args.log_level, rank)
    logger = MMLogger.get_instance('standalone_inference_ddp', log_level=args.log_level)

    if rank == 0:
        logger.info("开始DDP分布式推理...")
        logger.info(f"数据路径: {args.data_path}")
        logger.info(f"配置文件: {args.config}")
        logger.info(f"模型权重: {args.checkpoint}")
        logger.info(f"批处理大小: {args.batch_size}")
        logger.info(f"分布式配置: rank={rank}, world_size={world_size}, local_rank={local_rank}")

    try:
        # 初始化分布式推理引擎
        engine = DistributedInferenceEngine(
            config_path=args.config,
            checkpoint_path=args.checkpoint,
            local_rank=local_rank,
            rank=rank,
            world_size=world_size
        )

        # 初始化分布式数据加载器
        data_loader = DistributedStandaloneDataLoader(
            data_path=args.data_path,
            test_pipeline=engine.test_pipeline,
            batch_size=args.batch_size,
            num_workers=args.num_workers,
            gt_label=args.gt_label,
            rank=rank,
            world_size=world_size
        )

        # 初始化分布式结果管理器
        result_manager = DistributedResultManager(rank=rank, world_size=world_size)
        result_manager.stats['start_time'] = time.time()

        # 开始推理
        if rank == 0:
            logger.info("开始分布式批量推理...")

        # 同步所有进程
        if is_distributed:
            dist.barrier()

        # 使用tqdm显示进度（只在rank 0显示）
        if rank == 0:
            pbar = tqdm(total=len(data_loader), desc="推理进度")

        for batch_data in data_loader:
            try:
                # 批量推理
                results = engine.inference_batch(batch_data)

                # 处理结果
                for i, result in enumerate(results):
                    filename = batch_data[i]['data_samples'].get('filename', f'sample_{i}')
                    default_gt = batch_data[i]['data_samples'].get('gt_label', torch.tensor(0)).__int__()

                    # 安全地获取预测分数
                    if hasattr(result, 'pred_score') and result.pred_score is not None:
                        pred_scores = result.pred_score.cpu().numpy()
                        pre_lab = np.argmax(pred_scores, axis=1)[0]
                        _score = np.round(softmax(pred_scores[0])[0], decimals=2)

                        # 写入预测标签（只在rank 0执行）
                        write_pred_labels(filename, pre_lab, _score, default_gt,
                                        pred_gt_txt=Path(args.data_path).parent / 'pred_gt_labels.txt')

                        # 移动错误样本（只在rank 0执行）
                        if not pre_lab == default_gt:
                            change_samples('Cut', Path(args.data_path), pkl_pres=(filename, pre_lab, _score))

                        result_manager.add_result(filename, pred_scores, success=True)
                    else:
                        if rank == 0:
                            logger.warning(f"样本 {filename} 没有有效的预测结果")
                        result_manager.add_result(filename, np.array([]), success=False)

                # 更新进度条（只在rank 0）
                if rank == 0:
                    pbar.update(len(batch_data))

            except Exception as e:
                if rank == 0:
                    logger.error(f"批次推理失败: {e}")
                    pbar.update(len(batch_data))

                # 记录失败的样本
                for data in batch_data:
                    filename = data.get('filename', 'unknown')
                    result_manager.add_result(filename, np.array([]), success=False)

        # 关闭进度条
        if rank == 0:
            pbar.close()

        # 同步所有进程
        if is_distributed:
            dist.barrier()

        # 设置结束时间
        result_manager.stats['end_time'] = time.time()

        # 打印统计信息（只在rank 0）
        result_manager.print_statistics()

        # 清理空文件夹（只在rank 0执行）
        if rank == 0:
            empty_file_del(args.data_path)
            logger.info("DDP分布式推理完成!")

    except Exception as e:
        if rank == 0:
            logger.error(f"推理过程中发生错误: {e}")
        sys.exit(1)
    finally:
        # 清理分布式环境
        cleanup_distributed()


if __name__ == '__main__':
    main()
