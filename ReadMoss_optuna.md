需求：
1. 需要了解以下内容，再进行需求理解和方案设计：
   (1). 关于optuna框架：
   官方文档：https://optuna.readthedocs.io/en/stable/index.html
   Github仓库：https://github.com/optuna/optuna
   (2). 关于mmaction2框架：https://github.com/open-mmlab/mmaction2
   (3). 本项目是基于mmaction2框架下新增的自定义模型：
       训练框架即为当前的项目路径 @/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0808， 训练采用的4卡(单卡用于测试流程)，
       模型使用的是 @/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0808/mmaction/models/recognizers/multimodal_recognizer.py
       训练脚本：@/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0808/tools/train_m2.py

2. 基于1的背景,将optuna框架接入自定义模型的训练流程中，实现自动调参功能。
3. 模型基于当前配置训练存在问题是验证集指标不够高，泛化能力还较差, 需要明确具体哪些参数的调整需要优先进行



实施要求：
1. 一起实施在conda activate torch的环境下进行，每次执行前先确认conda环境是否激活和GPU资源是否充足
2. 每次修改的内容记录在项目下的docs/optuna_超参调优框架.md文件中
3. 所有代码需要在mmaction2的框架下通过新增文件实现，不能修改现有模型配置文件
4. 不能影响mmaction2的原来的正常训练流程
5. 考虑实现增加optuna框架后，训练过程的可视化
6. 需要实现训练结果的自动保存

